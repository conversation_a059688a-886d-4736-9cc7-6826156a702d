<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>My Profile - Epic WC Arena</title>
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/profile-specific.css" />
  <link rel="stylesheet" href="/css/player-cards-enhanced.css" />
  <link rel="stylesheet" href="/css/achievements.css" />
<link rel="stylesheet" href="/css/emoji-picker.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
  
  <!-- Chart.js for Analytics -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="/js/chart-utils.js"></script>
</head>
<body>
  <div id="navbar-container"></div>
  
  <!-- Epic Notification Container -->
  <div class="notification-container" id="notification-container"></div>

  <main>
    <!-- Page Title -->
    <div class="page-header">
      <h1 class="page-title" data-theme="townhall">🏛️ Town Hall</h1>
    </div>

    <!-- Epic Profile Hero Section -->
    <section class="profile-hero">
      <div class="profile-hero-content fade-in">
        <div class="profile-avatar-section">
          <div class="profile-avatar-container">
            <div id="profile-avatar" class="profile-avatar">
              <!-- Avatar will be loaded here -->
              <div class="profile-status online" aria-label="Online status"></div>
            </div>
            <div class="profile-level-badge" id="user-level">Level 1</div>
            <!-- Change Avatar Button -->
            <button class="change-avatar-btn" id="change-avatar-btn" title="Change Avatar">
              <i class="fas fa-camera"></i>
            </button>
          </div>
          
          <!-- User info moved next to avatar -->
          <div class="profile-user-info">
            <div class="profile-username-row">
              <h1 class="profile-username" id="profile-username">Loading...</h1>
              
              <!-- Action buttons positioned to the right -->
              <div class="profile-action-buttons">
                <!-- Admin Panel Button (only shown for admins) -->
                <a href="/admin.html" id="profile-admin-link" class="admin-panel-btn" style="display: none;" title="Admin Panel">
                  <i class="fas fa-shield-alt"></i>
                  <span>Admin Panel</span>
                </a>

                <!-- Username change button -->
                <button class="change-username-btn" id="change-username-btn" title="Change Username">
                  <i class="fas fa-edit"></i>
                  <span>Change Username</span>
                </button>

                <!-- Delete Account Button -->
                <button class="delete-account-btn" id="delete-account-btn" title="Delete Account">
                  <i class="fas fa-trash-alt"></i>
                  <span>Delete Account</span>
                </button>
              </div>
            </div>
            
            <!-- Username change form (hidden by default) -->
            <div class="username-change-container" id="username-change-container" style="display: none;">
              <div class="username-change-info">
                <p><i class="fas fa-info-circle"></i> Username changes are limited:</p>
                <ul>
                  <li>Once within first 30 days of registration</li>
                  <li>Then once every 30 days thereafter</li>
                </ul>
              </div>
              <form id="username-change-form" class="username-change-form">
                <input type="text" id="new-username" name="newUsername" placeholder="Enter new username" maxlength="20" required>
                <button type="submit" class="btn-save-username">Save</button>
                <button type="button" class="btn-cancel-username" id="cancel-username-change">Cancel</button>
              </form>
              <div id="username-change-feedback" class="username-feedback"></div>
            </div>
            <p class="profile-email" id="profile-email">
              <span id="email-text"></span>
              <span class="email-privacy">(only shown to you)</span>
            </p>
            
            <!-- Condensed stats in one row -->
            <div class="profile-stats-condensed">
              <div class="stat-condensed">
                <i class="fas fa-crown stat-condensed-icon"></i>
                <div class="stat-condensed-value" id="highest-rank-name">Loading...</div>
                <div class="stat-condensed-label">Highest Rank</div>
              </div>
              <div class="stat-condensed">
                <i class="fas fa-trophy stat-condensed-icon"></i>
                <div class="stat-condensed-value" id="highest-mmr">0</div>
                <div class="stat-condensed-label">MMR</div>
              </div>
              <div class="stat-condensed">
                <i class="fas fa-chart-line stat-condensed-icon"></i>
                <div class="stat-condensed-value" id="wins-losses">0/0</div>
                <div class="stat-condensed-label">W/L</div>
              </div>
              <div class="stat-condensed">
                <i class="fas fa-percentage stat-condensed-icon"></i>
                <div class="stat-condensed-value" id="win-rate">0%</div>
                <div class="stat-condensed-label">Win Rate</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="profile-points">
          <div class="points-card" title="Honor Points: Earned by reporting defeats and good sportsmanship">
            <i class="fas fa-medal"></i>
            <span id="honor-points-small">0</span>
            <span>Honor</span>
          </div>
          <div class="points-card" title="Arena Gold: Earned from wins and losses, redeemable for prizes">
            <i class="fas fa-coins"></i>
            <span id="arena-points-small">0</span>
            <span>Gold</span>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Profile Content -->
    <div class="profile-main">
      
      <!-- Layout Controls -->
      <div class="layout-controls">
        <button class="layout-control-btn" id="minimize-all" title="Minimize All Sections">
          <i class="fas fa-compress-alt"></i>
          <span>Minimize All</span>
        </button>
        <button class="layout-control-btn" id="maximize-all" title="Maximize All Sections">
          <i class="fas fa-expand-alt"></i>
          <span>Maximize All</span>
        </button>
        <button class="layout-control-btn" id="save-layout" title="Save Layout">
          <i class="fas fa-save"></i>
          <span>Save Layout</span>
        </button>
      </div>

      <div class="profile-grid-container" id="profile-grid-container">
        <!-- About Me Section -->
        <section class="profile-section draggable-section fade-in" id="section-about-me" data-section="about-me" data-position="1">
          <div class="grid-position-indicator">Position 1</div>
          <div class="section-header">
            <div class="section-title">
              <i class="fas fa-user"></i>
              <span>About Me</span>
            </div>
            <div class="section-controls">
              <button class="section-control-btn minimize-toggle" title="Toggle content visibility" data-action="minimize">
                <span class="btn-text">Minimize</span>
              </button>
            </div>
          </div>
          
          <div class="section-content">
            <div class="bio-content" id="bio-content">
              <!-- Inline edit form (hidden by default) - MOVED TO TOP -->
              <div class="inline-edit-form d-none" id="bio-edit-form">
                <div class="form-group">
                  <label for="bio-textarea">Bio:</label>
                  <textarea id="bio-textarea" name="bio" rows="4" placeholder="Tell us about yourself..."></textarea>
                </div>
                <div class="form-grid">
                  <div class="form-group">
                    <label for="bio-dob">Date of Birth:</label>
                    <input type="date" id="bio-dob" name="dob" placeholder="Select your date of birth">
                  </div>
                  <div class="form-group">
                    <label for="bio-country">Country:</label>
                    <input type="text" id="bio-country" name="country" placeholder="Enter your country">
                  </div>
                  <div class="form-group">
                    <label for="bio-game">Favorite Game:</label>
                    <select id="bio-game" name="favorite_game">
                      <option value="">Select game</option>
                      <option value="wc1">WC: Orcs & Humans</option>
                      <option value="wc2">WC II: Tides of Darkness</option>
                      <option value="wc3">WC III: Reign of Chaos</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="bio-race">Favorite Race:</label>
                    <select id="bio-race" name="favorite_race">
                      <option value="">Select race</option>
                      <option value="human">Human</option>
                      <option value="orc">Orc</option>
                      <option value="night_elf">Night Elf</option>
                      <option value="undead">Undead</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label for="bio-tactics">Favorite Tactics:</label>
                    <select id="bio-tactics" name="favorite_tactics">
                      <option value="">Select tactics</option>
                      <option value="rush">Rush</option>
                      <option value="boom">Boom (Economic Focus)</option>
                      <option value="turtle">Turtle (Defensive)</option>
                      <option value="all-in">All-in Attack</option>
                      <option value="harassment">Harassment</option>
                      <option value="map-control">Map Control</option>
                      <option value="tech-rush">Tech Rush</option>
                      <option value="mass-units">Mass Units</option>
                      <option value="mixed-army">Mixed Army</option>
                      <option value="tower-rush">Tower Rush</option>
                      <option value="fast-expansion">Fast Expansion</option>
                      <option value="stealth-ambush">Stealth/Ambush</option>
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <small style="color: rgba(255, 255, 255, 0.6); font-style: italic;">
                    <i class="fas fa-info-circle"></i>
                    Top Allies and Opponents are automatically populated based on your highest ranked player's match history.
                  </small>
                </div>
                <div class="form-actions">
                  <button class="epic-btn" id="bio-save-btn">Save Changes</button>
                  <button class="edit-btn" id="bio-cancel-btn">Cancel</button>
                </div>
              </div>

              <!-- Bio display section -->
              <div class="bio-display" id="bio-display-content">
                <div class="bio-text-container">
                  <p id="bio-text" style="transition: 0.3s;">No bio yet. Click edit to add one!</p>
                  <button class="edit-btn" id="bio-edit-btn" title="Edit Profile">
                    <i class="fas fa-edit"></i>
                  </button>
                </div>
                
                <div class="bio-stats">
                  <div class="bio-stat">
                    <div class="bio-stat-label">Date of Birth</div>
                    <div class="bio-stat-value" id="profile-dob">Not specified</div>
                  </div>
                  <div class="bio-stat">
                    <div class="bio-stat-label">Country</div>
                    <div class="bio-stat-value" id="profile-country">Not specified</div>
                  </div>
                  <div class="bio-stat">
                    <div class="bio-stat-label">Favorite Game</div>
                    <div class="bio-stat-value" id="profile-game">Not specified</div>
                  </div>
                  <div class="bio-stat">
                    <div class="bio-stat-label">Favorite Race</div>
                    <div class="bio-stat-value" id="profile-race">Not specified</div>
                  </div>
                  <div class="bio-stat">
                    <div class="bio-stat-label">Favorite Tactics</div>
                    <div class="bio-stat-value" id="profile-tactics">Not specified</div>
                  </div>
                  <div class="bio-stat">
                    <div class="bio-stat-label">Top Allies</div>
                    <div class="bio-stat-value" id="profile-allies">Loading...</div>
                  </div>
                  <div class="bio-stat">
                    <div class="bio-stat-label">Top Opponents</div>
                    <div class="bio-stat-value" id="profile-enemies">Loading...</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Hero Tier Section -->
        <section class="profile-section draggable-section fade-in" id="section-hero-tier" data-section="hero-tier" data-position="2">
          <div class="grid-position-indicator">Position 2</div>
          <div class="section-header">
            <div class="section-title">
              <i class="fas fa-shield-alt"></i>
              <span>Hero Tier</span>
            </div>
            <div class="section-controls">
              <button class="section-control-btn minimize-toggle" title="Toggle content visibility" data-action="minimize">
                <span class="btn-text">Minimize</span>
              </button>
            </div>
          </div>
          
          <div class="section-content">
            <div class="hero-tier-container">
              <!-- Hero Status Display -->
              <div class="hero-status-card" id="hero-status-card">
                <div class="hero-status-icon" id="hero-status-icon">
                  <i class="fas fa-shield-alt"></i>
                </div>
                <div class="hero-status-info" id="hero-status-info">
                  <h3 id="hero-tier-name">Loading...</h3>
                  <p id="hero-tier-description">Checking your hero status...</p>
                </div>
                <div class="hero-status-badge" id="hero-status-badge">
                  <span id="hero-tier-level">-</span>
                </div>
              </div>

              <!-- Unlocked Images Display -->
              <div class="unlocked-images-section" id="unlocked-images-section">
                <h4><i class="fas fa-star"></i> Unlocked Profile Images</h4>
                <div class="unlocked-images-grid" id="unlocked-images-grid">
                  <!-- Unlocked images will be populated here -->
                </div>
              </div>

              <!-- Hero Action Button -->
              <div class="hero-action-section" id="hero-action-section">
                <button class="epic-btn hero-action-btn" id="hero-action-btn">
                  <i class="fas fa-crown"></i>
                  <span id="hero-action-text">Be a Hero</span>
                </button>
                <p class="hero-action-subtitle" id="hero-action-subtitle">
                  Unlock exclusive profile images and support the arena!
                </p>
              </div>
            </div>
          </div>
        </section>

        <!-- Content Creator Section -->
        <section class="profile-section draggable-section fade-in" id="section-content-creator" data-section="content-creator" data-position="3">
          <div class="grid-position-indicator">Position 3</div>
          <div class="section-header">
            <div class="section-title">
              <i class="fas fa-video"></i>
              <span>Watch Tower</span>
            </div>
            <div class="section-controls">
              <button class="section-control-btn" id="refresh-streaming-btn" title="Refresh live status and profile images">
                <i class="fas fa-sync-alt"></i>
                <span class="btn-text">Refresh Live Status</span>
              </button>
              <button class="section-control-btn minimize-toggle" title="Toggle content visibility" data-action="minimize">
                <span class="btn-text">Minimize</span>
              </button>
            </div>
          </div>
          
          <div class="section-content">
            <div class="platform-cards">
              <!-- YouTube Card -->
              <div class="platform-card">
                <div class="platform-header">
                  <div class="platform-icon youtube">
                    <i class="fab fa-youtube"></i>
                  </div>
                  <div class="platform-info">
                    <h4>YouTube Channel</h4>
                    <p id="youtube-status">Not connected</p>
                  </div>
                  <button class="edit-btn" id="youtube-edit-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                </div>
                
                <!-- YouTube Description (shown when connected) -->
                <div class="platform-description d-none" id="youtube-description-display">
                  <p id="youtube-description-text"></p>
                  <div class="content-types" id="youtube-content-types">
                    <!-- Content type badges will be added here -->
                  </div>
                </div>
                
                <!-- YouTube Edit Form (hidden by default) -->
                <div class="inline-edit-form d-none" id="youtube-edit-form">
                  <div class="form-group">
                    <label for="youtube-username">YouTube Username:</label>
                    <input type="text" id="youtube-username" name="youtube-username" placeholder="YourChannelName (without @)">
                  </div>
                  <div class="form-group">
                    <label for="youtube-description">Channel Description:</label>
                    <textarea id="youtube-description" name="youtube-description" rows="3" placeholder="Describe your YouTube channel..."></textarea>
                  </div>
                  <div class="form-group">
                    <label>Content Types:</label>
                    <div style="display: flex; gap: 1rem; margin-top: 0.5rem;">
                      <label style="display: flex; align-items: center; gap: 0.5rem; font-weight: normal;">
                        <input type="checkbox" id="youtube-wc12" name="youtube-wc12">
                        <span>WC 1 & 2</span>
                      </label>
                      <label style="display: flex; align-items: center; gap: 0.5rem; font-weight: normal;">
                        <input type="checkbox" id="youtube-wc3" name="youtube-wc3">
                        <span>WC 3</span>
                      </label>
                    </div>
                  </div>
                  <div class="form-actions">
                    <button class="epic-btn" id="youtube-save-btn">Save Changes</button>
                    <button class="edit-btn" id="youtube-cancel-btn">Cancel</button>
                  </div>
                </div>
              </div>
              
              <!-- Twitch Card -->
              <div class="platform-card">
                <div class="platform-header">
                  <div class="platform-icon twitch">
                    <i class="fab fa-twitch"></i>
                  </div>
                  <div class="platform-info">
                    <h4>Twitch Channel</h4>
                    <p id="twitch-status">Not connected</p>
                  </div>
                  <button class="edit-btn" id="twitch-edit-btn">
                    <i class="fas fa-edit"></i>
                  </button>
                </div>
                
                <!-- Twitch Description (shown when connected) -->
                <div class="platform-description d-none" id="twitch-description-display">
                  <p id="twitch-description-text"></p>
                  <div class="content-types" id="twitch-content-types">
                    <!-- Content type badges will be added here -->
                  </div>
                </div>
                
                <!-- Twitch Edit Form (hidden by default) -->
                <div class="inline-edit-form d-none" id="twitch-edit-form">
                  <div class="form-group">
                    <label for="twitch-username">Twitch Username:</label>
                    <input type="text" id="twitch-username" name="twitch-username" placeholder="YourTwitchName">
                  </div>
                  <div class="form-group">
                    <label for="twitch-description">Channel Description:</label>
                    <textarea id="twitch-description" name="twitch-description" rows="3" placeholder="Describe your Twitch channel..."></textarea>
                  </div>
                  <div class="form-group">
                    <label>Content Types:</label>
                    <div style="display: flex; gap: 1rem; margin-top: 0.5rem;">
                      <label style="display: flex; align-items: center; gap: 0.5rem; font-weight: normal;">
                        <input type="checkbox" id="twitch-wc12" name="twitch-wc12">
                        <span>WC 1 & 2</span>
                      </label>
                      <label style="display: flex; align-items: center; gap: 0.5rem; font-weight: normal;">
                        <input type="checkbox" id="twitch-wc3" name="twitch-wc3">
                        <span>WC 3</span>
                      </label>
                    </div>
                  </div>
                  <div class="form-actions">
                    <button class="epic-btn" id="twitch-save-btn">Save Changes</button>
                    <button class="edit-btn" id="twitch-cancel-btn">Cancel</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Player Names Section -->
        <section class="profile-section draggable-section fade-in" id="section-player-names" data-section="player-names" data-position="4">
          <div class="grid-position-indicator">Position 4</div>
          <div class="section-header">
            <div class="section-title">
              <i class="fas fa-fort-awesome"></i>
              <span>Barracks</span>
            </div>
            <div class="section-controls">
              <button class="section-control-btn minimize-toggle" title="Toggle content visibility" data-action="minimize">
                <span class="btn-text">Minimize</span>
              </button>
            </div>
          </div>
          
          <div class="section-content">
            <!-- Game Type Filter -->
            <div class="barracks-filter-bar">
              <div class="game-type-tabs">
                <button class="game-type-tab active" data-game-type="all">
                  <i class="fas fa-globe"></i>
                  <span>All Games</span>
                </button>
                <button class="game-type-tab" data-game-type="war1">
                  <i class="fas fa-sword"></i>
                  <span>War1</span>
                </button>
                <button class="game-type-tab" data-game-type="war2">
                  <i class="fas fa-shield-alt"></i>
                  <span>War2</span>
                </button>
                <button class="game-type-tab" data-game-type="war3">
                  <i class="fas fa-crown"></i>
                  <span>War3</span>
                </button>
              </div>
            </div>
            
            <div id="player-names-container">
              <div class="loading">Loading player names...</div>
            </div>
            
            <!-- Add Player Button moved inside section -->
            <div class="section-action-area">
              <button class="epic-btn" id="add-player-btn" title="Add a new player name">
                <i class="fas fa-plus"></i>
                <span>Add/Link Player</span>
              </button>
            </div>
          </div>
        </section>

        <!-- Clan Encampment Section -->
        <section class="profile-section draggable-section fade-in" id="section-clan-management" data-section="clan-management" data-position="5">
          <div class="grid-position-indicator">Position 5</div>
          <div class="section-header">
            <div class="section-title">
              <i class="fas fa-campground"></i>
              <span>Clan Encampment</span>
            </div>
            <div class="section-controls">
              <button class="section-control-btn minimize-toggle" title="Toggle content visibility" data-action="minimize">
                <span class="btn-text">Minimize</span>
              </button>
            </div>
          </div>
          
          <div class="section-content">
            <div id="clan-container">
              <!-- Simplified Clan Actions -->
              <div class="clan-encampment-actions">
                <button class="encampment-btn create-clan-btn" onclick="clanManager.showCreateClanModal()">
                  Create Clan
                </button>
                
                <button class="encampment-btn browse-clans-btn" onclick="clanManager.showBrowseClansModal()">
                  Browse Clans
                </button>
              </div>

              <!-- User's Clans Container -->
              <div id="user-clans-container">
                <div class="loading">Loading your clan encampment...</div>
              </div>
            </div>
          </div>
        </section>

        <!-- War Table Section -->
        <section class="profile-section draggable-section fade-in" id="section-campaign" data-section="campaign" data-position="6">
          <div class="grid-position-indicator">Position 6</div>
          <div class="section-header">
            <div class="section-title">
              <i class="fas fa-flag"></i>
              <span>War Table</span>
            </div>
            <div class="section-controls">
              <button class="section-control-btn minimize-toggle" title="Toggle content visibility" data-action="minimize">
                <span class="btn-text">Minimize</span>
              </button>
            </div>
          </div>
          
          <div class="section-content">
            <div class="campaign-section-header">
              <a href="/views/campaigns.html" class="epic-btn campaign-link-btn" title="Report to War Table">
                <i class="fas fa-flag"></i>
                <span>Report To War Table</span>
              </a>
            </div>

            <div class="campaign-progress-summary">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                  </div>
                  <div class="stat-value" id="missions-completed">0</div>
                  <div class="stat-label">Total Missions Completed</div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-flag-checkered"></i>
                  </div>
                  <div class="stat-value" id="campaigns-completed">0</div>
                  <div class="stat-label">Total Campaigns Completed</div>
                </div>
              </div>
            </div>

            <!-- Game Type Tabs -->
            <div class="war-table-tabs-container">
              <div class="war-table-tabs">
                <button class="war-table-tab" data-game="war1">
                  <i class="fas fa-dragon"></i>
                  <span>WC I</span>
                </button>
                <button class="war-table-tab active" data-game="war2">
                  <i class="fas fa-shield-alt"></i>
                  <span>WC II</span>
                </button>
                <button class="war-table-tab" data-game="war3">
                  <i class="fas fa-chess-king"></i>
                  <span>WC III</span>
                </button>
              </div>
            </div>

            <div class="campaign-progress-list" id="campaign-progress-container">
              <div class="loading">Loading campaign progress...</div>
            </div>
          </div>
        </section>

        <!-- Achievements Section -->
        <section class="profile-section draggable-section fade-in" id="section-achievements" data-section="achievements" data-position="7">
          <div class="grid-position-indicator">Position 7</div>
          <div class="section-header">
            <div class="section-title">
              <i class="fas fa-trophy"></i>
              <span>Achievements</span>
            </div>
            <div class="section-controls">
              <button class="section-control-btn minimize-toggle" title="Toggle content visibility" data-action="minimize">
                <span class="btn-text">Minimize</span>
              </button>
            </div>
          </div>
          
          <div class="section-content">
            <div class="achievement-summary">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-trophy"></i>
                  </div>
                  <div class="stat-value" id="total-achievements">0</div>
                  <div class="stat-label">Total</div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="stat-value" id="completed-achievements">0</div>
                  <div class="stat-label">Unlocked</div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon">
                    <i class="fas fa-percentage"></i>
                  </div>
                  <div class="stat-value" id="completion-percentage">0%</div>
                  <div class="stat-label">Complete</div>
                </div>
              </div>
            </div>

            <div class="achievement-tabs">
              <button class="achievement-tab active" data-tab="arena-achievements">
                <i class="fas fa-sword"></i>
                <span>Arena</span>
              </button>
              <button class="achievement-tab" data-tab="social-achievements">
                <i class="fas fa-users"></i>
                <span>Social</span>
              </button>
              <button class="achievement-tab" data-tab="wartable-achievements">
                <i class="fas fa-flag"></i>
                <span>War Table</span>
              </button>
            </div>

            <div class="achievement-content active" id="arena-achievements-tab">
              <div id="arena-achievements-container">
                <div class="loading">Loading arena achievements...</div>
              </div>
            </div>

            <div class="achievement-content" id="social-achievements-tab" style="display: none;">
              <div id="social-achievements-container">
                <div class="loading">Loading social achievements...</div>
              </div>
            </div>

            <div class="achievement-content" id="wartable-achievements-tab" style="display: none;">
              <!-- War Table Sub-tabs for WC1/WC2/WC3 -->
              <div class="wartable-achievement-tabs-container">
                <div class="wartable-achievement-tabs">
                  <button class="wartable-achievement-tab active" data-game="wc1">
                    <i class="fas fa-dragon"></i>
                    <span>WC I</span>
                  </button>
                  <button class="wartable-achievement-tab" data-game="wc2">
                    <i class="fas fa-shield-alt"></i>
                    <span>WC II</span>
                  </button>
                  <button class="wartable-achievement-tab" data-game="wc3">
                    <i class="fas fa-chess-king"></i>
                    <span>WC III</span>
                  </button>
                </div>
              </div>

              <!-- WC1 Achievements -->
              <div class="wartable-achievement-content active" id="wc1-achievements-content">
                <div id="wc1-achievements-container">
                  <div class="loading">Loading WC I achievements...</div>
                </div>
              </div>

              <!-- WC2 Achievements -->
              <div class="wartable-achievement-content" id="wc2-achievements-content" style="display: none;">
                <div id="wc2-achievements-container">
                  <div class="loading">Loading WC II achievements...</div>
                </div>
              </div>

              <!-- WC3 Achievements -->
              <div class="wartable-achievement-content" id="wc3-achievements-content" style="display: none;">
                <div id="wc3-achievements-container">
                  <div class="loading">Loading WC III achievements...</div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Cartography Section -->
        <section class="profile-section draggable-section fade-in" id="section-cartography" data-section="cartography" data-position="8">
          <div class="grid-position-indicator">Position 8</div>
          <div class="section-header">
            <div class="section-title">
              <i class="fas fa-map"></i>
              <span>Cartography</span>
            </div>
            <div class="section-controls">
              <button class="section-control-btn minimize-toggle" title="Toggle content visibility" data-action="minimize">
                <span class="btn-text">Minimize</span>
              </button>
            </div>
          </div>
          
          <div class="section-content">
            <!-- Atlas Link -->
            <div class="atlas-link-container">
              <a href="/views/maps.html" class="atlas-link-btn">
                <div class="atlas-link-icon">
                  <i class="fas fa-globe"></i>
                </div>
                <div class="atlas-link-content">
                  <div class="atlas-link-title">Explore the Atlas</div>
                  <div class="atlas-link-subtitle">Browse all maps in the community</div>
                </div>
                <div class="atlas-link-arrow">
                  <i class="fas fa-arrow-right"></i>
                </div>
              </a>
            </div>

            <!-- User's Maps -->
            <div class="user-maps-section">
              <div class="user-maps-header">
                <h3>My Maps</h3>
                <div class="user-maps-count" id="user-maps-count">Loading...</div>
              </div>
              
              <div class="user-maps-container" id="user-maps-container">
                <div class="loading">Loading your maps...</div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  </main>

  <!-- Add Player Modal -->
  <div id="add-player-modal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Add New Player</h2>
        <span class="close" id="close-add-player-modal">&times;</span>
      </div>
      <div class="modal-body">
        <form id="add-player-form">
          <div class="form-group">
            <label for="player-name">Player Name:</label>
            <input type="text" id="player-name" name="playerName" placeholder="Enter player name" required>
            <small>Enter the exact player name as it appears in-game</small>
          </div>
          <div class="form-actions">
            <button type="submit" class="epic-btn">Add Player</button>
            <button type="button" class="edit-btn" id="cancel-add-player">Cancel</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Clan Management Modals -->
  
  <!-- Create Clan Modal -->
  <div id="create-clan-modal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2><i class="fas fa-shield-alt"></i> Create Clan</h2>
        <span class="close" id="close-create-clan-modal">&times;</span>
      </div>
      <div class="modal-body">
        <form id="create-clan-form">
          <div class="form-group">
            <label for="clan-name">Clan Name:</label>
            <input type="text" id="clan-name" name="clanName" placeholder="Enter clan name" required maxlength="30">
            <small>Choose a unique name for your clan (3-30 characters)</small>
          </div>
          <div class="form-group">
            <label for="clan-tag">Clan Tag:</label>
            <input type="text" id="clan-tag" name="clanTag" placeholder="TAG" required maxlength="6" style="text-transform: uppercase;">
            <small>Short tag to represent your clan (2-6 characters)</small>
          </div>
          <div class="form-group">
            <label for="clan-description">Description:</label>
            <textarea id="clan-description" name="clanDescription" placeholder="Describe your clan..." rows="4"></textarea>
            <small>Tell others about your clan's goals and requirements</small>
          </div>
          <div class="form-group">
            <label for="clan-game">Primary Game:</label>
            <select id="clan-game" name="clanGame" required>
              <option value="">Select primary game</option>
              <option value="wc1">WC: Orcs & Humans</option>
              <option value="wc2">WC II</option>
              <option value="wc3">WC III</option>
            </select>
          </div>
          <div class="form-group">
            <label>
              <input type="checkbox" id="clan-private" name="clanPrivate">
              Private Clan (invitation only)
            </label>
          </div>
          <div class="form-actions">
            <button type="submit" class="epic-btn">
              <i class="fas fa-plus"></i>
              Create Clan
            </button>
            <button type="button" class="edit-btn" id="cancel-create-clan">Cancel</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Find Clans Modal -->
  <div id="find-clans-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 800px;">
      <div class="modal-header">
        <h2><i class="fas fa-search"></i> Find Clans</h2>
        <span class="close" id="close-find-clans-modal">&times;</span>
      </div>
      <div class="modal-body">
        <!-- Search Filters -->
        <div class="clan-search-filters">
          <div class="form-grid">
            <div class="form-group">
              <input type="text" id="clan-search-name" placeholder="Search by clan name...">
            </div>
            <div class="form-group">
              <select id="clan-search-game">
                <option value="">All Games</option>
                <option value="wc1">WC: Orcs & Humans</option>
                <option value="wc2">WC II</option>
                <option value="wc3">WC III</option>
              </select>
            </div>
            <div class="form-group">
              <button type="button" class="epic-btn" id="search-clans-btn">
                <i class="fas fa-search"></i>
                Search
              </button>
            </div>
          </div>
        </div>

        <!-- Clans List -->
        <div id="clans-list">
          <div class="loading">Loading available clans...</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Join Clan Modal -->
  <div id="join-clan-modal" class="modal" style="display: none;">
    <div class="modal-content">
      <div class="modal-header">
        <h2><i class="fas fa-handshake"></i> Join Clan</h2>
        <span class="close" id="close-join-clan-modal">&times;</span>
      </div>
      <div class="modal-body">
        <div id="join-clan-details">
          <!-- Clan details will be populated here -->
        </div>
        <div class="form-group">
          <label for="join-message">Application Message (Optional):</label>
          <textarea id="join-message" placeholder="Tell the clan leaders why you want to join..." rows="3"></textarea>
        </div>
        <div class="form-actions">
          <button type="button" class="epic-btn" id="confirm-join-clan">
            <i class="fas fa-check"></i>
            Send Request
          </button>
          <button type="button" class="edit-btn" id="cancel-join-clan">Cancel</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Player Stats Modal will be created dynamically by ModalManager -->

  <!-- Avatar Selection Modal -->
  <div id="avatar-selection-modal" class="modal" style="display: none;">
    <div class="modal-content" style="max-width: 700px;">
      <div class="modal-header">
        <h2><i class="fas fa-user-circle"></i> Change Avatar</h2>
        <span class="close" id="close-avatar-modal">&times;</span>
      </div>
      <div class="modal-body">
        <div class="avatar-options-container">
          
          <!-- Default Avatar Option -->
          <div class="avatar-option" data-type="default">
            <div class="avatar-preview">
              <img src="/assets/img/ranks/emblem.png" alt="Default Emblem">
              <div class="avatar-selection-indicator">
                <i class="fas fa-check"></i>
              </div>
            </div>
            <div class="avatar-info">
              <h3>Default Emblem</h3>
              <p>Classic WC Arena emblem</p>
            </div>
          </div>

          <!-- Highest Rank Option -->
          <div class="avatar-option" data-type="highest_rank" id="highest-rank-option">
            <div class="avatar-preview">
              <img src="/assets/img/ranks/emblem.png" alt="Highest Rank" id="highest-rank-preview">
              <div class="avatar-selection-indicator">
                <i class="fas fa-check"></i>
              </div>
              <div class="avatar-unavailable-overlay" id="rank-unavailable" style="display: none;">
                <span>No players linked</span>
              </div>
            </div>
            <div class="avatar-info">
              <h3>Highest Rank</h3>
              <p id="highest-rank-description">Use your highest rank</p>
            </div>
          </div>

          <!-- Custom Images Section -->
          <div class="custom-avatars-section">
            <h3 class="custom-section-title">
              <i class="fas fa-star"></i> Special Images
            </h3>
            <div class="custom-avatars-grid">
              
              <div class="avatar-option custom-option" data-type="custom" data-image="elf.png" data-tier="1">
                <div class="avatar-preview">
                  <img src="/assets/img/profiles/elf.png" alt="Elf">
                  <div class="avatar-selection-indicator">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="avatar-lock-overlay" style="display: none;">
                    <i class="fas fa-lock"></i>
                    <span>Hero Tier 1+</span>
                  </div>
                </div>
                <div class="avatar-info">
                  <h4>Elf</h4>
                  <p>Graceful forest guardian</p>
                </div>
              </div>

              <div class="avatar-option custom-option" data-type="custom" data-image="dwarf.png" data-tier="2">
                <div class="avatar-preview">
                  <img src="/assets/img/profiles/dwarf.png" alt="Dwarf">
                  <div class="avatar-selection-indicator">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="avatar-lock-overlay" style="display: none;">
                    <i class="fas fa-lock"></i>
                    <span>Hero Tier 2+</span>
                  </div>
                </div>
                <div class="avatar-info">
                  <h4>Dwarf</h4>
                  <p>Stout mountain warrior</p>
                </div>
              </div>

              <div class="avatar-option custom-option" data-type="custom" data-image="mage.png" data-tier="3">
                <div class="avatar-preview">
                  <img src="/assets/img/profiles/mage.png" alt="Mage">
                  <div class="avatar-selection-indicator">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="avatar-lock-overlay" style="display: none;">
                    <i class="fas fa-lock"></i>
                    <span>Hero Tier 3+</span>
                  </div>
                </div>
                <div class="avatar-info">
                  <h4>Mage</h4>
                  <p>Mystical spellcaster</p>
                </div>
              </div>

              <div class="avatar-option custom-option" data-type="custom" data-image="dragon.png" data-tier="4">
                <div class="avatar-preview">
                  <img src="/assets/img/profiles/dragon.png" alt="Dragon">
                  <div class="avatar-selection-indicator">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="avatar-lock-overlay" style="display: none;">
                    <i class="fas fa-lock"></i>
                    <span>Hero Tier 4</span>
                  </div>
                </div>
                <div class="avatar-info">
                  <h4>Dragon</h4>
                  <p>Mighty dragon lord</p>
                </div>
              </div>

              <div class="avatar-option custom-option" data-type="custom" data-image="paladin.png" data-tier="5">
                <div class="avatar-preview">
                  <img src="/assets/img/profiles/paladin.png" alt="Paladin">
                  <div class="avatar-selection-indicator">
                    <i class="fas fa-check"></i>
                  </div>
                  <div class="avatar-lock-overlay" style="display: none;">
                    <i class="fas fa-lock"></i>
                    <span>Donation Required</span>
                  </div>
                </div>
                <div class="avatar-info">
                  <h4>Paladin</h4>
                  <p>Righteous holy warrior</p>
                </div>
              </div>

            </div>
          </div>

        </div>

        <div class="modal-footer">
          <div class="form-actions">
            <button type="button" class="epic-btn" id="save-avatar-selection">
              <i class="fas fa-save"></i>
              Save Avatar
            </button>
            <button type="button" class="edit-btn" id="cancel-avatar-selection">Cancel</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Account Confirmation Modal -->
  <div id="delete-account-modal" class="modal delete-account-modal" style="display: none;">
    <div class="modal-content delete-account-modal-content">
      <div class="modal-header delete-account-modal-header">
        <h2><i class="fas fa-exclamation-triangle"></i> Delete Account</h2>
        <span class="close delete-account-modal-close" id="close-delete-account-modal">&times;</span>
      </div>
      <div class="modal-body">
        <div class="warning-message" style="background: rgba(220, 53, 69, 0.1); border: 1px solid rgba(220, 53, 69, 0.3); border-radius: 8px; padding: 1rem; margin-bottom: 1.5rem;">
          <h3 style="color: #dc3545; margin: 0 0 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-skull-crossbones"></i>
            PERMANENT DELETION WARNING
          </h3>
          <p style="margin: 0; color: #e2e8f0; line-height: 1.5;">
            This action is <strong>IRREVERSIBLE</strong>. Your account and all associated data will be permanently deleted.
          </p>
        </div>
        
        <div class="deletion-details" style="margin-bottom: 1.5rem;">
          <h4 style="color: #ffd700; margin-bottom: 1rem;">What will be deleted:</h4>
          <ul style="color: #e2e8f0; line-height: 1.6; margin: 0; padding-left: 1.5rem;">
            <li>Your user account and profile</li>
            <li>All achievement progress and stats</li>
            <li>Campaign completion records</li>
            <li>Forum posts and comments</li>
            <li>Chat messages and private messages</li>
            <li>Friends and social connections</li>
            <li>All user-specific data</li>
          </ul>
          
          <h4 style="color: #ffd700; margin: 1.5rem 0 1rem 0;">What will be preserved:</h4>
          <ul style="color: #e2e8f0; line-height: 1.6; margin: 0; padding-left: 1.5rem;">
            <li>Uploaded maps (unlinked from your account)</li>
            <li>All players (completely preserved, just unlinked from your account)</li>
            <li>Tournaments you created (preserved with all match history)</li>
            <li>All player tournament participation and results</li>
          </ul>
          
          <div style="background: rgba(255, 193, 7, 0.1); border: 1px solid rgba(255, 193, 7, 0.3); border-radius: 8px; padding: 1rem; margin-top: 1rem;">
            <p style="margin: 0; color: #ffc107; font-weight: 500;">
              <i class="fas fa-info-circle"></i>
              If you're in a clan, leadership will transfer to another member. If you're the only member, the clan will be deleted.
            </p>
          </div>
        </div>
        
        <div class="confirmation-input" style="margin-bottom: 1.5rem;">
          <label for="delete-confirmation" style="display: block; color: #e2e8f0; margin-bottom: 0.5rem; font-weight: 600;">
            Type "DELETE" to confirm:
          </label>
          <input type="text" id="delete-confirmation" placeholder="DELETE" 
                 style="width: 100%; padding: 0.75rem; background: rgba(255, 255, 255, 0.05); border: 1px solid rgba(255, 255, 255, 0.2); border-radius: 6px; color: white; font-family: monospace;">
        </div>
        
        <div class="form-actions" style="display: flex; gap: 1rem; justify-content: flex-end;">
          <button type="button" class="edit-btn" id="cancel-delete-account">
            <i class="fas fa-times"></i>
            Cancel
          </button>
          <button type="button" class="epic-btn" id="confirm-delete-account" 
                  style="background: #dc3545; opacity: 0.5; cursor: not-allowed;" disabled>
            <i class="fas fa-trash-alt"></i>
            Delete My Account Forever
          </button>
        </div>
      </div>
    </div>
  </div>

  <div id="footer-container"></div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
  <script src="/js/api-config.js"></script>
  <script src="/js/utils.js"></script>
  <script src="/js/notifications.js"></script>
  <script src="/js/modules/ModalManager.js"></script>
  <script src="/js/modules/CartographyManager.js"></script>
  <script src="/js/main.js"></script>
  <script src="/js/playerDetails.js"></script>
  <script src="/js/draggable-grid.js"></script>
  
  <!-- New Unified Achievement System -->
  <script type="module" src="/js/app.js"></script>
  
      <!-- Unified Membership Management -->
    <script src="/js/modules/MembershipManager.js?v=********-2"></script>
    
    <script>
      // Initialize unified MembershipManager when page loads
      document.addEventListener('DOMContentLoaded', async () => {
        console.log('🛡️ Initializing unified MembershipManager for profile page...');
        
        // Load user data to ensure navbar is properly updated
        if (window.loadUser && typeof window.loadUser === 'function') {
          await window.loadUser();
        }
        
        try {
          // Use the global membershipManager instance from MembershipManager.js
          if (window.membershipManager) {
            await window.membershipManager.init();
            console.log('✅ Unified MembershipManager initialized successfully for profile page');
          } else {
            console.error('❌ MembershipManager not found on window object');
          }
        } catch (error) {
          console.error('❌ Failed to initialize MembershipManager:', error);
        }
      });
      
      // Initialize unified navigation system
      window.addEventListener('load', async () => {
        console.log('🔄 Initializing navigation on myprofile page...');

        // Load unified navigation
        if (typeof window.loadNavigation === 'function') {
          await window.loadNavigation();
        } else if (typeof window.loadNavbar === 'function') {
          await window.loadNavbar();
        }

        // Update navbar profile
        setTimeout(async () => {
          if (window.updateNavbarProfileUnified) {
            console.log('🔄 Updating navbar profile (unified) on myprofile page');
            await window.updateNavbarProfileUnified();
          } else if (window.updateNavbarProfile) {
            console.log('🔄 Updating navbar profile (legacy) on myprofile page');
            await window.updateNavbarProfile();
          }

          // Check admin status and show admin link if user is admin
          await checkAndShowAdminLink();
        }, 500);
      });

      // Function to check admin status and show admin link
      async function checkAndShowAdminLink() {
        try {
          console.log('🔐 Checking admin status for profile page...');

          // Check if we're in Electron mode
          const isElectronMode = window !== window.top || window.location.search.includes('electron=true');
          console.log('🔍 Environment check:', { isElectronMode });

          let user = null;

          // Try to get user data from Electron AuthManager first
          if (isElectronMode && window._appInstance) {
            const authManager = window._appInstance.getComponent('auth');
            if (authManager && authManager.getUser()) {
              user = authManager.getUser();
              console.log('✅ Got user data from Electron AuthManager:', user.username);
            }
          }

          // Fallback to API call if no user data from Electron
          if (!user) {
            const response = await fetch('/api/me', {
              credentials: 'include',
              headers: { 'Accept': 'application/json' }
            });

            if (response.ok) {
              user = await response.json();
              console.log('✅ Got user data from API:', user.username);
            }
          }

          if (user) {
            const adminLink = document.getElementById('profile-admin-link');

            if (adminLink) {
              console.log('🔍 User role check:', { role: user.role, isAdmin: user.role === 'admin' });

              if (user.role === 'admin') {
                adminLink.style.display = 'flex';
                console.log('✅ Admin link shown on profile page');

                // Update link for Electron mode
                if (isElectronMode) {
                  // In Electron, use the full URL path
                  adminLink.href = '/admin.html';
                  adminLink.target = '_self'; // Open in same window for Electron
                  console.log('🔧 Updated admin link for Electron mode');
                } else {
                  // In web browser, can open in new tab
                  adminLink.href = '/admin.html';
                  adminLink.target = '_blank';
                  console.log('🔧 Updated admin link for web mode');
                }
              } else {
                adminLink.style.display = 'none';
                console.log('👤 Admin link hidden - user is not admin');
              }
            } else {
              console.warn('⚠️ Admin link element not found');
            }
          }
        } catch (error) {
          console.error('❌ Error checking admin status:', error);
          // Hide admin link on error
          const adminLink = document.getElementById('profile-admin-link');
          if (adminLink) {
            adminLink.style.display = 'none';
          }
        }
      }
    </script>
  
  <script type="module" src="/js/myprofile-refactored.js"></script>
  
  <script>
    console.log('🎮 Profile page ready for modular initialization!');
  </script>
</body>
</html> 