/* ==========================================================================
   ENHANCED MATCHES TAB STYLING
   Modern, sleek design with improved UX and visual hierarchy
   ========================================================================== */

/* ===== MATCHES TAB CONTAINER ===== */
.matches-content {
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.95), 
    rgba(30, 41, 59, 0.9));
  border-radius: 16px;
  border: 1px solid rgba(212, 175, 55, 0.15);
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  position: relative;
}

.matches-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(212, 175, 55, 0.6) 50%, 
    transparent 100%);
  z-index: 1;
}

/* ===== MATCHES LIST CONTAINER ===== */
.matches-list {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(212, 175, 55, 0.5) transparent;
}

.matches-list::-webkit-scrollbar {
  width: 6px;
}

.matches-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.matches-list::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.5);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.matches-list::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.7);
}

/* ===== ENHANCED MATCH ITEMS ===== */
.match-item {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.04), 
    rgba(255, 255, 255, 0.02));
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 1rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  cursor: pointer;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.match-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(212, 175, 55, 0.4) 50%, 
    transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.match-item:hover {
  transform: translateY(-4px) scale(1.02);
  border-color: rgba(212, 175, 55, 0.3);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(212, 175, 55, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.match-item:hover::before {
  opacity: 1;
}

/* Win/Loss specific styling */
.match-item.match-win {
  border-left: 4px solid #22c55e;
  background: linear-gradient(135deg, 
    rgba(34, 197, 94, 0.08), 
    rgba(255, 255, 255, 0.02));
}

.match-item.match-win:hover {
  border-color: rgba(34, 197, 94, 0.4);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(34, 197, 94, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.match-item.match-loss {
  border-left: 4px solid #ef4444;
  background: linear-gradient(135deg, 
    rgba(239, 68, 68, 0.08), 
    rgba(255, 255, 255, 0.02));
}

.match-item.match-loss:hover {
  border-color: rgba(239, 68, 68, 0.4);
  box-shadow: 
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(239, 68, 68, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* ===== ENHANCED MATCH HEADER ===== */
.match-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
}

.match-header::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(212, 175, 55, 0.3) 50%, 
    transparent 100%);
}

.match-outcome {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 700;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
}

.match-outcome::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent);
  transition: left 0.6s ease;
}

.match-item:hover .match-outcome::before {
  left: 100%;
}

.match-win .match-outcome {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.match-loss .match-outcome {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.match-outcome i {
  font-size: 1rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.match-type {
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.15), 
    rgba(212, 175, 55, 0.08));
  color: #D4AF37;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.8rem;
  border: 1px solid rgba(212, 175, 55, 0.2);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.match-date {
  color: var(--neutral-400, #94a3b8);
  font-size: 0.8rem;
  font-weight: 500;
  padding: 0.3rem 0.6rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.match-expand-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  border-radius: 50%;
  color: #D4AF37;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.match-expand-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(212, 175, 55, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
}

.match-item:hover .match-expand-icon {
  background: rgba(212, 175, 55, 0.2);
  border-color: rgba(212, 175, 55, 0.4);
  transform: scale(1.1);
  color: #FFD700;
}

.match-item:hover .match-expand-icon::before {
  width: 100%;
  height: 100%;
}

.match-expand-icon i {
  font-size: 0.8rem;
  position: relative;
  z-index: 1;
  transition: transform 0.3s ease;
}

.match-item:hover .match-expand-icon i {
  transform: scale(1.2);
}

/* ===== ENHANCED MATCH DETAILS ===== */
.match-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.match-map {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--neutral-200, #e2e8f0);
  font-weight: 500;
  font-size: 0.9rem;
  padding: 0.4rem 0.8rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.match-map i {
  color: #D4AF37;
  font-size: 0.9rem;
}

.match-players-container {
  flex: 1;
  min-width: 0;
}

/* ===== ENHANCED PLAYER DISPLAY ===== */
.match-players {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.match-players.team-match {
  flex-direction: column;
  gap: 0.5rem;
}

.team {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.team::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.05), 
    transparent);
  transition: left 0.6s ease;
}

.team:hover::before {
  left: 100%;
}

.team.winning-team {
  background: rgba(34, 197, 94, 0.08);
  border-color: rgba(34, 197, 94, 0.2);
}

.team.losing-team {
  background: rgba(239, 68, 68, 0.08);
  border-color: rgba(239, 68, 68, 0.2);
}

.team:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.team-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.team-label {
  font-size: 0.7rem;
  color: var(--neutral-400, #94a3b8);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.team-result {
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
}

.team-result.winner {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.team-result.loser {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.team-players {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.team-vs-separator {
  color: var(--neutral-400, #94a3b8);
  font-weight: 700;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.player-link,
.current-player {
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  color: var(--neutral-200, #e2e8f0);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.85rem;
  padding: 0.3rem 0.6rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.player-link::before,
.current-player::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 175, 55, 0.1), 
    transparent);
  transition: left 0.4s ease;
}

.player-link:hover::before,
.current-player:hover::before {
  left: 100%;
}

.player-link:hover,
.current-player:hover {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  color: #D4AF37;
  transform: translateY(-1px);
}

.player-link.winner,
.current-player.winner {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.player-link.loser,
.current-player.loser {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.current-player {
  background: rgba(212, 175, 55, 0.15);
  border: 1px solid rgba(212, 175, 55, 0.3);
  color: #D4AF37;
  font-weight: 600;
}

.current-player .fa-star {
  color: #FFD700;
  font-size: 0.7rem;
  animation: starPulse 2s ease-in-out infinite;
}

@keyframes starPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.player-separator {
  color: var(--neutral-500, #64748b);
  font-weight: 600;
  margin: 0 0.2rem;
}

.vs-separator {
  color: var(--neutral-400, #94a3b8);
  font-weight: 700;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.2rem 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.winner-icon {
  color: #22c55e;
  font-size: 0.7rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.loser-icon {
  color: #ef4444;
  font-size: 0.7rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.match-type-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: linear-gradient(135deg, 
    rgba(212, 175, 55, 0.2), 
    rgba(212, 175, 55, 0.1));
  color: #D4AF37;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.6rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(212, 175, 55, 0.3);
  z-index: 2;
}

/* ===== ENHANCED MMR CHANGE ===== */
.mmr-change {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  font-weight: 700;
  font-size: 0.9rem;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  border: 1px solid;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mmr-change::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent);
  transition: left 0.4s ease;
}

.mmr-change:hover::before {
  left: 100%;
}

.mmr-change.positive {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
  border-color: rgba(34, 197, 94, 0.2);
}

.mmr-change.negative {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.2);
}

.mmr-change i {
  font-size: 0.8rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* ===== ENHANCED PAGINATION ===== */
.matches-pagination {
  background: linear-gradient(135deg, 
    rgba(15, 23, 42, 0.95), 
    rgba(30, 41, 59, 0.9));
  border-top: 1px solid rgba(212, 175, 55, 0.2);
  padding: 1.5rem;
  position: relative;
}

.matches-pagination::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(212, 175, 55, 0.4) 50%, 
    transparent 100%);
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.matches-range {
  font-weight: 600;
  color: var(--neutral-200, #e2e8f0);
  font-size: 0.9rem;
}

.page-info {
  font-weight: 700;
  color: #D4AF37;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.page-numbers {
  display: flex;
  gap: 0.25rem;
  align-items: center;
  background: rgba(255, 255, 255, 0.03);
  padding: 0.5rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.page-numbers .btn {
  min-width: 2.2rem;
  height: 2.2rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.page-numbers .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.1), 
    transparent);
  transition: left 0.4s ease;
}

.page-numbers .btn:hover::before {
  left: 100%;
}

.page-numbers .btn-primary {
  background: linear-gradient(135deg, #D4AF37, #FFD700);
  color: #000;
  border: 1px solid #D4AF37;
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.3);
}

.page-numbers .btn-primary:hover {
  background: linear-gradient(135deg, #FFD700, #FFED4E);
  border-color: #FFD700;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
}

.page-numbers .btn-secondary {
  background: rgba(255, 255, 255, 0.08);
  color: var(--neutral-200, #e2e8f0);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.page-numbers .btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(212, 175, 55, 0.3);
  color: #D4AF37;
  transform: translateY(-1px);
}

.page-numbers .btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.page-ellipsis {
  color: var(--neutral-400, #94a3b8);
  font-weight: 600;
  padding: 0 0.5rem;
  font-size: 0.8rem;
}

/* Previous/Next buttons */
.pagination-controls .btn {
  font-size: 0.8rem;
  padding: 0.5rem 1rem;
  min-height: auto;
  border-radius: 6px;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  color: #D4AF37;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pagination-controls .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(212, 175, 55, 0.2), 
    transparent);
  transition: left 0.4s ease;
}

.pagination-controls .btn:hover::before {
  left: 100%;
}

.pagination-controls .btn:hover:not(:disabled) {
  background: rgba(212, 175, 55, 0.2);
  border-color: rgba(212, 175, 55, 0.4);
  color: #FFD700;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

.pagination-controls .btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  transform: none;
}

.pagination-controls .btn i {
  font-size: 0.7rem;
  margin: 0 0.2rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .matches-list {
    padding: 1rem;
    max-height: 50vh;
  }
  
  .match-item {
    padding: 1rem;
    margin-bottom: 0.75rem;
  }
  
  .match-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .match-outcome {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
  
  .match-type {
    font-size: 0.7rem;
    padding: 0.3rem 0.6rem;
  }
  
  .match-date {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
  }
  
  .match-expand-icon {
    width: 28px;
    height: 28px;
  }
  
  .match-expand-icon i {
    font-size: 0.7rem;
  }
  
  .match-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .match-map {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
  
  .match-players.team-match {
    gap: 0.4rem;
  }
  
  .team {
    padding: 0.4rem 0.6rem;
  }
  
  .team-header {
    margin-bottom: 0.2rem;
  }
  
  .team-label {
    font-size: 0.6rem;
  }
  
  .team-result {
    font-size: 0.6rem;
    padding: 0.15rem 0.3rem;
  }
  
  .team-players {
    gap: 0.4rem;
  }
  
  .player-link,
  .current-player {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .team-vs-separator {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
  
  .vs-separator {
    font-size: 0.7rem;
    padding: 0.15rem 0.4rem;
  }
  
  .mmr-change {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
  
  .match-type-badge {
    top: 0.5rem;
    right: 0.5rem;
    font-size: 0.5rem;
    padding: 0.15rem 0.4rem;
  }
  
  .pagination-info {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
    margin-bottom: 0.75rem;
  }
  
  .matches-range,
  .page-info {
    font-size: 0.8rem;
  }
  
  .pagination-controls {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .page-numbers {
    justify-content: center;
    padding: 0.4rem;
  }
  
  .page-numbers .btn {
    min-width: 1.8rem;
    height: 1.8rem;
    font-size: 0.7rem;
  }
  
  .pagination-controls .btn {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
  }
  
  .page-ellipsis {
    font-size: 0.7rem;
    padding: 0 0.3rem;
  }
}

@media (max-width: 480px) {
  .matches-list {
    padding: 0.75rem;
  }
  
  .match-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
  }
  
  .match-header {
    gap: 0.5rem;
  }
  
  .match-outcome {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .match-type {
    font-size: 0.65rem;
    padding: 0.25rem 0.5rem;
  }
  
  .match-date {
    font-size: 0.65rem;
    padding: 0.15rem 0.4rem;
  }
  
  .match-expand-icon {
    width: 24px;
    height: 24px;
  }
  
  .match-expand-icon i {
    font-size: 0.6rem;
  }
  
  .match-details {
    gap: 0.5rem;
  }
  
  .match-map {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .team {
    padding: 0.3rem 0.5rem;
  }
  
  .team-label {
    font-size: 0.55rem;
  }
  
  .team-result {
    font-size: 0.55rem;
    padding: 0.1rem 0.25rem;
  }
  
  .team-players {
    gap: 0.3rem;
  }
  
  .player-link,
  .current-player {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }
  
  .team-vs-separator {
    font-size: 0.65rem;
    padding: 0.15rem 0.3rem;
  }
  
  .vs-separator {
    font-size: 0.65rem;
    padding: 0.1rem 0.3rem;
  }
  
  .mmr-change {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }
  
  .match-type-badge {
    top: 0.4rem;
    right: 0.4rem;
    font-size: 0.45rem;
    padding: 0.1rem 0.3rem;
  }
  
  .pagination-info {
    padding: 0.5rem 0.75rem;
    margin-bottom: 0.5rem;
  }
  
  .matches-range,
  .page-info {
    font-size: 0.75rem;
  }
  
  .page-numbers {
    padding: 0.3rem;
  }
  
  .page-numbers .btn {
    min-width: 1.6rem;
    height: 1.6rem;
    font-size: 0.65rem;
  }
  
  .pagination-controls .btn {
    font-size: 0.65rem;
    padding: 0.3rem 0.6rem;
  }
  
  .page-ellipsis {
    font-size: 0.65rem;
    padding: 0 0.2rem;
  }
}

/* ===== LOADING STATES ===== */
.match-item.loading {
  opacity: 0.7;
  pointer-events: none;
}

.match-item.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  border: 2px solid rgba(212, 175, 55, 0.3);
  border-top: 2px solid #D4AF37;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* ===== ACCESSIBILITY ===== */
.match-item[role="button"] {
  cursor: pointer;
}

.match-item[role="button"]:focus {
  outline: 2px solid #D4AF37;
  outline-offset: 2px;
}

/* ===== REDUCED MOTION ===== */
@media (prefers-reduced-motion: reduce) {
  .match-item,
  .match-outcome,
  .player-link,
  .current-player,
  .mmr-change,
  .page-numbers .btn,
  .pagination-controls .btn {
    transition: none;
  }
  
  .match-item::before,
  .match-outcome::before,
  .player-link::before,
  .current-player::before,
  .mmr-change::before,
  .page-numbers .btn::before,
  .pagination-controls .btn::before {
    display: none;
  }
  
  .match-item:hover,
  .player-link:hover,
  .current-player:hover,
  .mmr-change:hover,
  .page-numbers .btn:hover,
  .pagination-controls .btn:hover {
    transform: none;
  }
} 