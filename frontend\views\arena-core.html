<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WC Arena Core - Desktop Companion</title>
  
  <!-- Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/black-theme.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="/css/navbar-universal.css" />
  
  <style>
    .arena-core-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      margin-top: 80px;
      text-align: center;
    }
    
    .hero-section {
      background: linear-gradient(135deg, #2c3e50, #34495e);
      border-radius: 12px;
      padding: 40px;
      margin-bottom: 30px;
      color: white;
    }
    
    .hero-title {
      font-size: 2.5em;
      font-weight: 700;
      margin-bottom: 15px;
      color: #f39c12;
    }
    
    .hero-subtitle {
      font-size: 1.2em;
      color: #bdc3c7;
      margin-bottom: 30px;
    }
    
    .download-section {
      background: linear-gradient(135deg, #8e44ad, #9b59b6);
      border-radius: 12px;
      padding: 40px;
      text-align: center;
      color: white;
      margin: 30px 0;
    }
    
    .download-title {
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 20px;
    }
    
    .download-description {
      font-size: 1.1em;
      margin-bottom: 30px;
      color: #ecf0f1;
    }
    
    .download-btn {
      display: inline-block;
      background: linear-gradient(135deg, #f39c12, #e67e22);
      color: white;
      padding: 18px 40px;
      border-radius: 30px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.2em;
      margin: 10px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    
    .download-btn:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }
    
    .features-section {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      padding: 40px;
      margin: 30px 0;
    }
    
    .features-title {
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 30px;
      color: #ecf0f1;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 25px;
      margin: 30px 0;
    }
    
    .feature-card {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 12px;
      padding: 30px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      transition: all 0.3s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }
    
    .feature-icon {
      font-size: 3em;
      margin-bottom: 20px;
      color: #f39c12;
    }
    
    .feature-title {
      font-size: 1.4em;
      font-weight: 600;
      margin-bottom: 15px;
      color: #ecf0f1;
    }
    
    .feature-description {
      color: #bdc3c7;
      line-height: 1.6;
      font-size: 1.1em;
    }
    
    .cta-section {
      background: linear-gradient(135deg, #27ae60, #2ecc71);
      border-radius: 12px;
      padding: 40px;
      text-align: center;
      color: white;
      margin: 30px 0;
    }
    
    .cta-title {
      font-size: 1.8em;
      font-weight: 600;
      margin-bottom: 15px;
    }
    
    .cta-description {
      font-size: 1.1em;
      margin-bottom: 25px;
      opacity: 0.9;
    }
    
    .cta-btn {
      display: inline-block;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      padding: 15px 30px;
      border-radius: 25px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1em;
      border: 2px solid rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;
    }
    
    .cta-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }
  </style>
</head>
<body>
  <!-- Navbar will be loaded here -->
  <div id="navbar-container"></div>

  <div class="arena-core-container">
    <!-- Hero Section -->
    <div class="hero-section">
      <h1 class="hero-title">WC Arena Core</h1>
      <p class="hero-subtitle">The Ultimate Desktop Companion for Warcraft Players</p>
    </div>

    <!-- Download Section -->
    <div class="download-section">
      <h2 class="download-title">Download WC Arena Core</h2>
      <p class="download-description">
        Get the full desktop experience with game detection, screenshot management, and seamless integration with WC Arena.
      </p>
      <a href="/views/download-launcher.html" class="download-btn">
        <i class="fas fa-download"></i>
        Download Now
      </a>
    </div>

    <!-- Key Features Section -->
    <div class="features-section">
      <h2 class="features-title">Key Features</h2>
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <i class="fas fa-gamepad"></i>
          </div>
          <h3 class="feature-title">Game Manager</h3>
          <p class="feature-description">
            Automatically detect and launch your Warcraft games. Track game sessions, manage multiple game versions, and get instant access to your favorite titles.
          </p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <i class="fas fa-camera"></i>
          </div>
          <h3 class="feature-title">Screenshot Manager</h3>
          <p class="feature-description">
            Intelligent screenshot organization and analysis. Automatically categorize screenshots by game, detect victories and defeats, and keep your gaming memories organized.
          </p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <h3 class="feature-title">Match Tracking</h3>
          <p class="feature-description">
            Track your match results automatically. Get detailed statistics, performance insights, and share your achievements with the community.
          </p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">
            <i class="fas fa-users"></i>
          </div>
          <h3 class="feature-title">Community Integration</h3>
          <p class="feature-description">
            Seamlessly connect with the WC Arena community. Share screenshots, track leaderboards, and stay updated with the latest news and events.
          </p>
        </div>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="cta-section">
      <h2 class="cta-title">Ready to Enhance Your Warcraft Experience?</h2>
      <p class="cta-description">
        Join thousands of players who have already upgraded their gaming setup with WC Arena Core.
      </p>
      <a href="/views/download-launcher.html" class="cta-btn">
        <i class="fas fa-rocket"></i>
        Get Started Now
      </a>
    </div>
  </div>

  <!-- Scripts -->
  <script src="/js/app.js"></script>
  
  <script>
    // Wait for app to be ready before loading navbar
    document.addEventListener('DOMContentLoaded', async function() {
      // Wait for app initialization to complete
      await new Promise(resolve => {
        const checkAppReady = () => {
          if (window._appInstance && window.apiClient) {
            console.log('✅ App ready, loading navbar');
            resolve();
          } else {
            console.log('⏳ Waiting for app to be ready...');
            setTimeout(checkAppReady, 100);
          }
        };
        checkAppReady();
      });
      
      // Wait for auth manager to be ready and user data loaded
      await new Promise(resolve => {
        const checkAuthReady = () => {
          // Check if auth manager exists and has loaded user data
          if (window._appInstance && window._appInstance.getComponent('auth')) {
            const authManager = window._appInstance.getComponent('auth');
            if (authManager.initialized) {
              console.log('✅ Auth manager ready, user data loaded');
              resolve();
            } else {
              console.log('⏳ Waiting for auth manager to initialize...');
              setTimeout(checkAuthReady, 100);
            }
          } else {
            console.log('⏳ Waiting for auth manager component...');
            setTimeout(checkAuthReady, 100);
          }
        };
        checkAuthReady();
      });
      
      // Load user data to ensure navbar is properly updated
      if (window.loadUser && typeof window.loadUser === 'function') {
        await window.loadUser();
      }

      try {
        console.log('🔄 Initializing navigation on arena-core page...');

        // Load unified navigation
        if (typeof window.loadNavigation === 'function') {
          await window.loadNavigation();
        } else if (typeof window.loadNavbar === 'function') {
          await window.loadNavbar();
        } else {
          // Fallback to manual loading
          const navbarResponse = await fetch('/components/navbar.html');
          if (navbarResponse.ok) {
            const navbarHtml = await navbarResponse.text();
            document.getElementById('navbar-container').innerHTML = navbarHtml;
            console.log('✅ Navbar HTML loaded successfully');

            const script = document.createElement('script');
            script.src = '/js/unified-navigation.js';
            script.onload = function() {
              console.log('✅ Unified navigation script loaded, initializing...');
              if (window.initUnifiedNavigation) {
                window.initUnifiedNavigation();
              }
            };
            document.head.appendChild(script);
          }
        }

        // Update navbar profile
        setTimeout(async () => {
          if (window.updateNavbarProfileUnified) {
            console.log('🔄 Updating navbar profile (unified) on arena-core page');
            await window.updateNavbarProfileUnified();
          } else if (window.updateNavbarProfile) {
            console.log('🔄 Updating navbar profile (legacy) on arena-core page');
            await window.updateNavbarProfile();
          }
        }, 500);

      } catch (error) {
        console.warn('Could not load navigation:', error);
      }
    });
  </script>
</body>
</html> 