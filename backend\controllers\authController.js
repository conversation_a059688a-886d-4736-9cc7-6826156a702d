const User = require('../models/User');
const jwt = require('jsonwebtoken');

exports.handleOAuthCallback = async (req, res, next) => {
  console.log('🚀 === OAUTH CALLBACK START ===');
  console.log('🔍 Session ID:', req.sessionID);
  console.log('🔍 Full session object:', JSON.stringify(req.session, null, 2));
  console.log('🔍 Query params:', req.query);
  console.log('🔍 Headers:', {
    userAgent: req.headers['user-agent'],
    referer: req.headers.referer
  });
  console.log('🔍 User object exists:', !!req.user);
  console.log('🔍 User details:', req.user && {
    id: req.user._id,
    username: req.user.username,
    email: req.user.email
  });
  console.log('=================================');

  const user = req.user;
  let isElectron = false;
  let clientState = '';

  // Parse the state parameter to extract electron flag and client state
  try {
    const stateParam = req.query.state;
    if (stateParam) {
      // Handle both encoded and non-encoded state parameters
      let decodedState = stateParam;
      try {
        decodedState = decodeURIComponent(stateParam);
      } catch (decodeError) {
        // If decode fails, use original state param
        console.log('🔍 State parameter not URL encoded, using as-is');
      }
      
      const parsedState = JSON.parse(decodedState);
      isElectron = parsedState.isElectron === true;
      clientState = parsedState.clientState || '';
      console.log('🔍 Parsed OAuth state:', {
        isElectron: isElectron,
        clientState: clientState,
        rawState: stateParam,
        decodedState: decodedState
      });
    }
  } catch (error) {
    console.log('⚠️ Failed to parse OAuth state parameter:', error.message);
    console.log('🔍 Raw state param:', req.query.state);
  }
  
  // Additional Electron detection from user agent and query params
  const userAgent = req.get('User-Agent') || '';
  const queryElectron = req.query.electron === 'true' || req.query.embedded === 'true';
  const userAgentElectron = userAgent.includes('WC_Arena_Core') || userAgent.includes('Electron');
  
  if (queryElectron || userAgentElectron) {
    isElectron = true;
    console.log('🔍 Additional Electron detection:', {
      queryElectron,
      userAgentElectron,
      userAgent: userAgent.substring(0, 100),
      finalIsElectron: isElectron
    });
  }

  console.log('🔍 Processing OAuth callback:', { hasUser: !!user, isElectron, clientState });

  if (!user) {
    console.log('❌ OAuth callback: No user found');
    return res.redirect('/views/index.html?error=auth_failed');
  }

  // Set authentication in session
  req.session.isAuthenticated = true;
  req.session.userId = user._id.toString();

  console.log('User authenticated:', {
    id: user._id,
    username: user.username,
    email: user.email,
    provider: user.provider,
    isElectron
  });

  // Save session and handle redirect
  req.session.save((err) => {
    if (err) {
      console.log('❌ Session save error:', err);
      return res.redirect('/views/index.html?error=session_error');
    }

    console.log('Session saved successfully:', {
      sessionID: req.sessionID,
      userId: req.session.userId,
      isAuthenticated: req.session.isAuthenticated
    });

    if (isElectron) {
      console.log('🔧 Handling simplified Electron OAuth flow');
      
      // Generate transfer token for Electron (will be claimed by frontend)
      const payload = {
        userId: user._id.toString(),
        username: user.username,
        email: user.email,
        type: 'transfer', // Transfer token type for frontend claiming
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (5 * 60) // 5 minutes (transfer tokens are short-lived)
      };

      const token = jwt.sign(payload, process.env.JWT_SECRET || 'your-secret-key');
      console.log('🎫 Generated transfer token for Electron client');

      // Create a page that redirects to custom protocol and closes the browser tab
      const redirectUri = `warcraftarena://oauth-success?token=${token}&state=${encodeURIComponent(JSON.stringify({isElectron: true, clientState: clientState}))}`;
      console.log('🎫 Creating redirect page for Electron with tab closing:', redirectUri);

      return res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Login Success - Redirecting to App</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
              color: white;
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 100vh;
              margin: 0;
              text-align: center;
            }
            .container {
              background: rgba(255, 255, 255, 0.1);
              padding: 40px;
              border-radius: 15px;
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
              max-width: 400px;
            }
            .spinner {
              border: 3px solid rgba(255, 255, 255, 0.3);
              border-top: 3px solid white;
              border-radius: 50%;
              width: 40px;
              height: 40px;
              animation: spin 1s linear infinite;
              margin: 20px auto;
            }
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <h2>🎉 Login Successful!</h2>
            <div class="spinner"></div>
            <p>Redirecting to Warcraft Arena...</p>
            <p><small>This tab will close automatically.</small></p>
          </div>

          <script>
            console.log('🔄 Electron OAuth success page loaded');

            // Redirect to custom protocol immediately
            window.location.href = '${redirectUri}';

            // Try to close the tab after a short delay
            setTimeout(() => {
              console.log('🔄 Attempting to close browser tab...');

              // Multiple methods to try to close the tab
              try {
                // Method 1: Standard window.close()
                window.close();

                // Method 2: If that doesn't work, try with opener
                if (window.opener) {
                  window.opener = null;
                  window.close();
                }

                // Method 3: Try to navigate away to about:blank
                setTimeout(() => {
                  if (!window.closed) {
                    console.log('🔄 Tab still open, navigating to about:blank');
                    window.location.href = 'about:blank';
                  }
                }, 1000);

              } catch (error) {
                console.log('⚠️ Could not close tab automatically:', error);
                // Show message to user
                document.body.innerHTML = \`
                  <div class="container">
                    <h2>✅ Login Complete!</h2>
                    <p>You can now close this browser tab and return to the Warcraft Arena app.</p>
                    <button onclick="window.close()" style="
                      background: rgba(255, 255, 255, 0.2);
                      border: 1px solid rgba(255, 255, 255, 0.3);
                      color: white;
                      padding: 10px 20px;
                      border-radius: 5px;
                      cursor: pointer;
                      font-size: 16px;
                      margin-top: 20px;
                    ">Close Tab</button>
                  </div>
                \`;
              }
            }, 2000); // 2 second delay to ensure redirect happens first
          </script>
        </body>
        </html>
      `);
    } else {
      console.log('🌐 Handling web OAuth flow (not Electron)');
      console.log('User login successful, redirecting to Game Manager');
      return res.redirect('/views/game-manager.html');
    }
  });
};

exports.setupUsername = async (req, res, next) => {
  let { username } = req.body;
  const user = req.user;

  if (!user) {
    return res.status(401).json({ error: 'Not authenticated' });
  }

  if (!username || !/^[a-zA-Z0-9_-]{3,20}$/.test(username)) {
    return res.status(400).json({ error: 'Invalid username format' });
  }

  // Convert to uppercase
  username = username.toUpperCase();

  try {
    // Check if username is already taken by another user
    const existingUser = await User.findOne({ username: username });
    if (existingUser && existingUser._id.toString() !== user._id.toString()) {
      return res.status(400).json({ error: 'Username already taken' });
    }

    // Set the username and mark as defined
    user.username = username;
    user.isUsernameDefined = true;

    // Save with validation
    try {
      await user.save();

      // Auto-create WC1 player for new user
      await createWC1PlayerForUser(user._id, username);

      // Update session if needed
      if (req.session) {
        req.session.touch();
      }

      res.json({ success: true, message: 'Username set successfully' });
    } catch (validationError) {
      console.error('Username validation error:', validationError);
      return res.status(400).json({
        error: validationError.message || 'Username validation failed',
        details: validationError.errors
      });
    }
  } catch (err) {
    console.error('Username setup error:', err);
    next(err);
  }
};

/**
 * Auto-create WC1 player for user
 */
async function createWC1PlayerForUser(userId, username) {
  try {
    const Player = require('../models/Player');
    
    // Check if WC1 player already exists
    const existingPlayer = await Player.findOne({
      user: userId,
      gameType: 'warcraft1'
    });

    if (existingPlayer) {
      console.log(`✅ WC1 player already exists for user ${username}`);
      return existingPlayer;
    }

    // Create new WC1 player
    const wc1Player = new Player({
      name: username,
      user: userId,
      gameType: 'warcraft1',
      mmr: 1200, // Default MMR for WC1
      wins: 0,
      losses: 0,
      isActive: true,
      autoCreated: true,
      createdAt: new Date()
    });

    await wc1Player.save();
    console.log(`✅ Auto-created WC1 player for user ${username}`);
    return wc1Player;
  } catch (error) {
    console.error(`❌ Error creating WC1 player for user ${username}:`, error);
    // Don't throw error - user creation should succeed even if player creation fails
  }
}

exports.logout = (req, res, next) => {
  req.logout(err => {
    if (err) return next(err);
    res.redirect('/');
  });
};
