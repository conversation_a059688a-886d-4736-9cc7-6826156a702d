{"name": "newsite-backend", "version": "1.0.0", "description": "Backend for newsite application", "main": "index.js", "scripts": {"start": "node index.js", "start-https": "node start-https.js", "dev": "nodemon index.js", "dev-https": "nodemon start-https.js", "mongo-express": "node start-mongo-express.js", "seed": "node scripts/seed-database.js", "db:admin": "node start-mongo-express.js", "db:seed": "node scripts/seed-database.js", "db:reset": "node scripts/seed-database.js", "css:analyze": "node scripts/analyze-css.js", "maps:cleanup": "node scripts/cleanupMapsData.js", "maps:import": "node scripts/enhancedImportMaps.js", "maps:reimport": "npm run maps:cleanup && npm run maps:import", "forum:update-categories": "node update-forum-categories.js", "fix:avatar-paths": "node scripts/fix-avatar-paths.js"}, "dependencies": {"@paypal/checkout-server-sdk": "^1.0.3", "axios": "^1.10.0", "bcrypt": "^5.1.1", "canvas": "^3.1.0", "child_process": "^1.0.2", "chokidar": "^3.5.3", "connect-mongo": "^5.1.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cron": "^4.3.0", "dotenv": "^10.0.0", "express": "^4.17.1", "express-session": "^1.18.1", "geoip-lite": "^1.4.10", "googleapis": "^149.0.0", "jimp": "^0.22.10", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mime-types": "^2.1.35", "moment": "^2.29.4", "mongoose": "^6.0.12", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-discord": "^0.1.4", "passport-google-oauth20": "^2.0.0", "passport-twitch-new": "^0.0.3", "sharp": "^0.32.6", "socket.io": "^4.8.1", "squareup": "^1.0.0", "tesseract.js": "^4.1.4", "xml2js": "^0.6.2"}, "devDependencies": {"nodemon": "^3.1.10"}}