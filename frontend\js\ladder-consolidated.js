/**
 * Consolidated Ladder System Frontend Implementation
 * Clean, organized implementation of ladder functionality
 */

class LadderManager {
  constructor() {
    this.currentGame = 'warcraft2';
    this.currentMatchType = 'all';
    this.currentPage = 1;
    this.totalPages = 1;
    this.searchQuery = '';
    this.chartInstances = {};
    
    this.init();
  }

  async init() {
    console.log('🎯 Initializing Ladder Manager...');
    
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
    } else {
      this.setupEventListeners();
    }
    
    // Load initial data
    await this.loadInitialData();
  }

  setupEventListeners() {
    // Game toggle button
    const gameToggleBtn = document.getElementById('game-toggle-btn');
    if (gameToggleBtn) {
      gameToggleBtn.addEventListener('click', () => this.toggleGame());
    }

    // Report match button
    const reportBtn = document.getElementById('report-match-btn');
    if (reportBtn) {
      reportBtn.addEventListener('click', () => this.openReportModal());
    }

    // Search functionality - Enhanced with real-time search
    const searchBtn = document.getElementById('search-btn');
    const searchInput = document.getElementById('player-search');
    if (searchBtn && searchInput) {
      searchBtn.addEventListener('click', () => this.searchPlayers());
      searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') this.searchPlayers();
      });
      
      // Add real-time search that resets when field is empty
      searchInput.addEventListener('input', (e) => {
        const value = e.target.value.trim();
        const clearBtn = document.getElementById('clear-search-btn');
        
        // Show/hide clear button based on input content
        if (clearBtn) {
          clearBtn.style.display = value.length > 0 ? 'flex' : 'none';
        }
        
        if (value === '') {
          // Reset to show all players when search is cleared
          console.log('🔄 Search cleared, resetting to show all players');
          this.searchQuery = '';
          this.currentPage = 1;
          this.loadLeaderboard(this.currentMatchType, this.currentPage, '');
        }
      });
      
      // Clear search button functionality
      const clearBtn = document.getElementById('clear-search-btn');
      if (clearBtn) {
        // Initially hide clear button
        clearBtn.style.display = 'none';
        
        clearBtn.addEventListener('click', () => {
          searchInput.value = '';
          clearBtn.style.display = 'none';
          this.searchQuery = '';
          this.currentPage = 1;
          this.loadLeaderboard(this.currentMatchType, this.currentPage, '');
          searchInput.focus(); // Return focus to search input
        });
      }
    }

    // Filter buttons
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
      btn.addEventListener('click', (e) => this.filterByMatchType(e.target.dataset.matchType));
    });

    // Pagination
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    if (prevBtn) prevBtn.addEventListener('click', () => this.previousPage());
    if (nextBtn) nextBtn.addEventListener('click', () => this.nextPage());

    // Modal close buttons
    document.querySelectorAll('.close-modal').forEach(btn => {
      btn.addEventListener('click', (e) => this.closeModal(e.target.closest('.modal')));
    });

    // Modal overlay clicks
    document.querySelectorAll('.modal').forEach(modal => {
      modal.addEventListener('click', (e) => {
        if (e.target === modal || e.target.classList.contains('modal-overlay')) {
          this.closeModal(modal);
        }
      });
    });

    console.log('✅ Event listeners set up');
  }

  async loadInitialData() {
    try {
      console.log('📊 Loading initial ladder data...');
      
      // Load data in parallel for better performance
      await Promise.all([
        this.loadRanks(),
        this.loadLeaderboard(),
        this.loadRecentMatches(),
        this.loadStats()
      ]);
      
      console.log('✅ Initial data loaded successfully');
    } catch (error) {
      console.error('❌ Error loading initial data:', error);
      this.loadMockData();
    }
  }

  async loadRanks() {
    try {
      const response = await fetch('/api/ladder/ranks');
      if (!response.ok) throw new Error('Failed to fetch ranks');
      
      const ranks = await response.json();
      this.displayRanks(ranks);
    } catch (error) {
      console.error('Error loading ranks:', error);
      this.displayMockRanks();
    }
  }

  displayRanks(ranks) {
    const container = document.getElementById('ranks-container');
    if (!container) return;

    // Reverse the ranks array to show Champion first, then down to Bronze
    const reversedRanks = [...ranks].reverse();

    container.innerHTML = reversedRanks.map(rank => `
      <div class="rank-item">
        <img src="${rank.image}" 
             alt="${rank.name}" 
             class="rank-image" 
             onerror="this.style.display='none'">
        <div class="rank-details">
          <h4 class="rank-name">${rank.name}</h4>
          <p class="rank-threshold">${rank.threshold}+ MMR</p>
        </div>
      </div>
    `).join('');
  }

  async loadLeaderboard(matchType = 'all', page = 1, search = '') {
    try {
      console.log(`🎯 Loading leaderboard: ${matchType}, page ${page}, search: "${search}"`);
      
      const params = new URLSearchParams({
        gameType: this.currentGame,
        matchType,
        limit: 10,  // Changed: Use 10 players per page instead of 20
        page: page  // Fixed: Add page parameter
      });
      
      if (search) params.append('search', search);
      
      // Fixed: Use the correct API endpoint that supports pagination
      const response = await fetch(`/api/ladder/rankings?${params}`);
      if (!response.ok) throw new Error('Failed to fetch leaderboard');
      
      const data = await response.json();
      console.log('📊 Leaderboard API response:', data);
      
      // Handle both old format (array) and new format (object with players/pagination)
      let players, totalPages, filterInfo;
      if (Array.isArray(data)) {
        // Fallback for old API format
        players = data;
        totalPages = Math.ceil(players.length / 10);  // Changed: Use 10 for pagination calculation
        console.log('⚠️ Using fallback pagination logic');
      } else {
        // New API format with proper pagination
        players = data.players || [];
        totalPages = data.pagination?.pages || 1;
        filterInfo = data.filter || {};
        console.log(`✅ Loaded ${players.length} players, page ${page} of ${totalPages}`);
      }
      
      // Update the leaderboard header with current filter info
      this.updateLeaderboardHeader(matchType, filterInfo);
      
      this.displayLeaderboard(players);
      this.updatePagination(page, totalPages);
    } catch (error) {
      console.error('Error loading leaderboard:', error);
      this.displayMockLeaderboard();
    }
  }

  updateLeaderboardHeader(matchType, filterInfo) {
    const header = document.querySelector('.leaderboard-section h2');
    if (!header) return;

    const gameTypeLabel = this.currentGame === 'warcraft2' ? 'WC II' : 'WC III';
    const matchTypeLabel = matchType === 'all' ? 'All Match Types' : matchType.toUpperCase();
    
    let headerText = `${gameTypeLabel} Leaderboard - ${matchTypeLabel}`;
    
    // Add additional info if available from the enhanced API
    if (filterInfo && filterInfo.totalPlayersInMatchType !== undefined) {
      if (matchType === 'all') {
        headerText += ` (${filterInfo.totalPlayersInMatchType} Total Players)`;
      } else {
        headerText += ` (${filterInfo.totalPlayersInMatchType} Players in ${matchType})`;
      }
    }
    
    header.textContent = headerText;
    console.log(`📊 Updated leaderboard header: ${headerText}`);
  }

  displayLeaderboard(players) {
    const tbody = document.getElementById('leaderboard-body');
    if (!tbody) return;

    if (!players || players.length === 0) {
      tbody.innerHTML = '<tr><td colspan="6" class="loading-cell">No players found</td></tr>';
      return;
    }

    console.log('🎯 Displaying leaderboard with players:', players.length);
    if (players.length > 0) {
      console.log('📊 Sample player data structure:', players[0]);
    }

    tbody.innerHTML = players.map((player, index) => {
      // Log the first few players to understand data structure
      if (index < 3) {
        console.log(`📊 Player ${index + 1} (${player.name}) data:`, {
          wins: player.wins,
          losses: player.losses,
          totalGames: player.totalGames,
          stats: player.stats,
          matchTypeStats: player.matchTypeStats
        });
      }

      // Calculate total games - try multiple approaches
      let totalGames = 0;
      let wins = 0;
      let losses = 0;

      // Method 1: Check if totalGames is directly available
      if (player.totalGames !== undefined) {
        totalGames = player.totalGames;
        wins = player.wins || 0;
        losses = player.losses || 0;
      }
      // Method 2: Check player.stats for overall stats
      else if (player.stats) {
        totalGames = player.stats.totalMatches || player.stats.matches || player.stats.gamesPlayed || 0;
        wins = player.stats.wins || 0;
        losses = player.stats.losses || 0;
      }
      // Method 3: Check specific match type stats (if viewing filtered results)
      else if (player.matchTypeStats) {
        totalGames = player.matchTypeStats.matches || 0;
        wins = player.matchTypeStats.wins || 0;
        losses = player.matchTypeStats.losses || 0;
      }
      // Method 4: Calculate from wins + losses as fallback
      else {
        wins = player.wins || 0;
        losses = player.losses || 0;
        totalGames = wins + losses;
      }

      const winRate = totalGames > 0 
        ? ((wins / totalGames) * 100).toFixed(1)
        : '0.0';
      
      const winRateClass = this.getWinRateClass(parseFloat(winRate));

      // Log calculation for first few players
      if (index < 3) {
        console.log(`📊 Player ${index + 1} (${player.name}) calculated stats:`, {
          totalGames,
          wins,
          losses,
          winRate: `${winRate}%`
        });
      }

      // Fix image path - check if it already includes the path
      let rankImageSrc = '/assets/img/ranks/unranked.png'; // default
      if (player.rank_image) {
        rankImageSrc = player.rank_image.startsWith('/') || player.rank_image.startsWith('http') 
          ? player.rank_image 
          : `/assets/img/ranks/${player.rank_image}`;
      } else if (player.rank && player.rank.image) {
        rankImageSrc = player.rank.image.startsWith('/') || player.rank.image.startsWith('http')
          ? player.rank.image
          : `/assets/img/ranks/${player.rank.image}`;
      } else {
        rankImageSrc = `/assets/img/ranks/unranked.png`;
      }

      return `
        <tr onclick="window.ladderManager?.showPlayerStats(${JSON.stringify(player).replace(/"/g, '&quot;')})">
          <td class="player-rank">
            <img src="${rankImageSrc}" 
                 alt="${player.rank_name || 'Unranked'}" 
                 class="player-rank-image" 
                 onerror="this.style.display='none'">
          </td>
          <td class="player-name">${player.name}</td>
          <td class="player-mmr">${player.mmr || 1000}</td>
          <td class="player-wins">${wins}</td>
          <td class="player-losses">${losses}</td>
          <td class="player-ratio ${winRateClass}">${winRate}%</td>
        </tr>
      `;
    }).join('');
  }

  getWinRateClass(winRate) {
    if (winRate >= 75) return 'excellent';
    if (winRate >= 60) return 'good';
    if (winRate >= 45) return 'average';
    return 'poor';
  }

  async loadRecentMatches() {
    try {
      const params = new URLSearchParams({
        gameType: this.currentGame,
        limit: '10'
      });
      
      const response = await fetch(`/api/ladder/matches?${params}`);
      if (!response.ok) throw new Error('Failed to fetch recent matches');
      
      const matches = await response.json();
      this.displayRecentMatches(matches); // Use all matches since we limited to 10
    } catch (error) {
      console.error('Error loading recent matches:', error);
      this.displayMockMatches();
    }
  }

  displayRecentMatches(matches) {
    const container = document.getElementById('recent-matches-container');
    if (!container) return;

    if (!matches || matches.length === 0) {
      container.innerHTML = '<div class="loading">No recent matches found</div>';
      return;
    }

    console.log('📊 Displaying recent matches:', matches); // Debug log

    container.innerHTML = matches.map((match, matchIndex) => {
      // Debug log for first few matches to understand structure
      if (matchIndex < 3) {
        console.log(`🔍 Match ${matchIndex + 1} structure:`, {
          matchType: match.matchType,
          winner: match.winner,
          players: match.players,
          map: match.map
        });
      }

      // Safely access map name
      const mapName = match.map?.name || match.mapName || 'Unknown Map';
      
      // Get resource level with fallback
      const resourceLevel = match.resourceLevel || 'medium';
      
      // Format match date
      const matchDate = this.formatDate(match.date || match.createdAt);
      
      // Get match type
      const matchType = match.matchType || '1v1';
      
      // Process players data
      const playersHtml = match.players?.map((player, playerIndex) => {
        // Try multiple ways to get player name
        let playerName = 'Unknown Player';
        let playerId = null;
        
        if (player.playerId?.name) {
          playerName = player.playerId.name;
          playerId = player.playerId._id || player.playerId;
        } else if (player.name) {
          playerName = player.name;
          playerId = player.playerId;
        } else if (typeof player.playerId === 'string') {
          playerId = player.playerId;
          playerName = player.playerId;
        }
        
        // Debug log for first few players to understand structure
        if (matchIndex < 2 && playerIndex < 2) {
          console.log(`🔍 Player ${playerIndex + 1} in match ${matchIndex + 1}:`, {
            name: playerName,
            playerId: playerId,
            playerIdType: typeof playerId,
            winner: match.winner,
            winnerType: typeof match.winner,
            team: player.team,
            placement: player.placement,
            isWinner: player.isWinner
          });
        }
        
        // Determine if player won - simplified logic based on actual data structure
        let isWinner = false;
        
        if (match.matchType === '1v1') {
          // For 1v1, winner should be a player ID
          if (typeof match.winner === 'string') {
            isWinner = (match.winner === playerId) || (match.winner === player.playerId?._id);
          } else if (match.winner && typeof match.winner === 'object') {
            isWinner = (match.winner.toString() === playerId?.toString()) || 
                      (match.winner.toString() === player.playerId?.toString());
          }
        } else if (match.matchType.includes('v')) {
          // For team matches (2v2, 3v3, 4v4), winner should be team number
          if (typeof match.winner === 'number') {
            isWinner = (match.winner === player.team);
          }
        } else if (match.matchType === 'ffa') {
          // For FFA, check placement (1st place = winner) or if winner is player ID
          if (player.placement === 1) {
            isWinner = true;
          } else if (typeof match.winner === 'string') {
            isWinner = (match.winner === playerId) || (match.winner === player.playerId?._id);
          }
        }
        
        // Fallback: check if player has explicit isWinner flag
        if (player.isWinner === true) {
          isWinner = true;
        }
        
        // Debug log winner determination for first few players
        if (matchIndex < 2 && playerIndex < 2) {
          console.log(`🎯 Winner determination for ${playerName}: ${isWinner ? 'WIN' : 'LOSS'}`);
        }
        
        return `
          <div class="player-result">
            <span class="player-name">${playerName}</span>
            <span class="player-mmr-change ${isWinner ? 'win' : 'loss'}">
              ${isWinner ? 'WIN' : 'LOSS'}
            </span>
          </div>
        `;
      }).join('') || '<div class="player-result"><span class="player-name">No player data</span></div>';

      return `
        <div class="match-card">
          <div class="match-header">
            <span class="match-type">${matchType}</span>
            <span class="match-date">${matchDate}</span>
          </div>
          <div class="match-details">
            <span class="match-map">${mapName}</span>
            <span class="match-resource">${resourceLevel} Resources</span>
          </div>
          <div class="match-players">
            ${playersHtml}
          </div>
        </div>
      `;
    }).join('');
  }

  async loadStats() {
    try {
      const params = new URLSearchParams({
        gameType: this.currentGame
      });
      
      const response = await fetch(`/api/ladder/global-stats?${params}`);
      if (!response.ok) throw new Error('Failed to fetch stats');
      
      const stats = await response.json();
      console.log('📊 Enhanced stats received:', stats);
      this.displayStats(stats);
      this.createCharts(stats);
    } catch (error) {
      console.error('Error loading stats:', error);
      this.displayMockStats();
    }
  }

  displayStats(stats) {
    console.log('📊 Displaying enhanced stats:', stats);
    
    // Update overview stats
    if (stats.overview) {
      this.updateOverviewStats(stats.overview);
    }
    
    // Update race stats with the new format
    const raceList = document.getElementById('race-stats-list');
    if (raceList && stats.races) {
      raceList.innerHTML = stats.races
        .map(race => `
          <li>
            <span class="stats-name">${this.formatRaceName(race.race)}</span>
            <span class="stats-value">${race.count} (${race.winRate?.toFixed(1) || 0}% WR)</span>
          </li>
        `).join('');
    }

    // Update match type stats with percentages
    const modesList = document.getElementById('modes-stats-list');
    if (modesList && stats.matchTypes) {
      modesList.innerHTML = stats.matchTypes
        .map(type => `
          <li>
            <span class="stats-name">${type.type?.toUpperCase() || 'Unknown'}</span>
            <span class="stats-value">${type.count} (${type.percentage?.toFixed(1) || 0}%)</span>
          </li>
        `).join('');
    }

    // Update map stats with enhanced data
    const mapsList = document.getElementById('maps-stats-list');
    if (mapsList && stats.maps) {
      mapsList.innerHTML = stats.maps.slice(0, 5)
        .map(map => `
          <li>
            <span class="stats-name">${map.name || 'Unknown Map'}</span>
            <span class="stats-value">${map.count} matches</span>
          </li>
        `).join('');
    }
  }

  updateOverviewStats(overview) {
    // Create or update overview section
    let overviewContainer = document.getElementById('overview-stats');
    if (!overviewContainer) {
      // Create overview section if it doesn't exist
      overviewContainer = document.createElement('div');
      overviewContainer.id = 'overview-stats';
      overviewContainer.className = 'stats-section';
      overviewContainer.innerHTML = `
        <h3>Overview</h3>
        <ul id="overview-stats-list"></ul>
      `;
      
      // Insert before existing stats sections
      const statsContainer = document.querySelector('.stats-container');
      if (statsContainer) {
        statsContainer.insertBefore(overviewContainer, statsContainer.firstChild);
      }
    }

    const overviewList = document.getElementById('overview-stats-list');
    if (overviewList && overview) {
      const gameTypeLabel = this.currentGame === 'warcraft2' ? 'WC2' : 'WC3';
      
      overviewList.innerHTML = `
        <li><span class="stats-name">Total ${gameTypeLabel} Players</span><span class="stats-value">${overview.totalPlayers || 0}</span></li>
        <li><span class="stats-name">Active Players</span><span class="stats-value">${overview.activePlayers || 0}</span></li>
        <li><span class="stats-name">Total Matches</span><span class="stats-value">${overview.totalMatches || 0}</span></li>
        <li><span class="stats-name">Avg Matches/Player</span><span class="stats-value">${overview.insights?.avgMatchesPerPlayer || 0}</span></li>
        <li><span class="stats-name">Highest MMR</span><span class="stats-value">${overview.insights?.highestMmr || 0}</span></li>
      `;
    }
  }

  formatRaceName(race) {
    if (!race) return 'Unknown';
    return race.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  createCharts(stats) {
    console.log('📊 Creating charts with enhanced stats:', stats);

    // Destroy existing charts if they exist
    if (this.charts) {
      Object.values(this.charts).forEach(chart => {
        if (chart && typeof chart.destroy === 'function') {
          chart.destroy();
        }
      });
    }
    this.charts = {};

    // Create Race Distribution Chart
    if (stats.races && stats.races.length > 0) {
      const raceCanvas = document.getElementById('race-chart');
      if (raceCanvas) {
        this.charts.race = this.createChart('race-chart', {
          type: 'doughnut',
          data: {
            labels: stats.races.map(race => this.formatRaceName(race.race)),
            datasets: [{
              data: stats.races.map(race => race.count),
              backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)'
              ],
              borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 205, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)'
              ],
              borderWidth: 2
            }]
          },
          options: {
            ...this.getChartOptions(),
            plugins: {
              title: {
                display: true,
                text: 'Race Distribution',
                color: '#e0e0e0'
              },
              legend: {
                display: true,
                position: 'bottom',
                labels: { color: '#e0e0e0' }
              }
            }
          }
        });
      }
    }

    // Create Match Type Distribution Chart
    if (stats.matchTypes && stats.matchTypes.length > 0) {
      const matchTypeCanvas = document.getElementById('match-type-chart');
      if (matchTypeCanvas) {
        this.charts.matchType = this.createChart('match-type-chart', {
          type: 'bar',
          data: {
            labels: stats.matchTypes.map(type => type.type?.toUpperCase() || 'Unknown'),
            datasets: [{
              label: 'Matches',
              data: stats.matchTypes.map(type => type.count),
              backgroundColor: 'rgba(54, 162, 235, 0.8)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            }]
          },
          options: {
            ...this.getChartOptions(),
            plugins: {
              title: {
                display: true,
                text: 'Match Type Distribution',
                color: '#e0e0e0'
              },
              legend: { display: false }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: { color: '#e0e0e0' },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
              },
              x: {
                ticks: { color: '#e0e0e0' },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
              }
            }
          }
        });
      }
    }

    // Create Rank Distribution Chart (new)
    if (stats.rankDistribution && stats.rankDistribution.length > 0) {
      const rankCanvas = document.getElementById('rank-chart');
      if (rankCanvas) {
        this.charts.rank = this.createChart('rank-chart', {
          type: 'bar',
          data: {
            labels: stats.rankDistribution.map(rank => rank.rank || 'Unranked'),
            datasets: [{
              label: 'Players',
              data: stats.rankDistribution.map(rank => rank.count),
              backgroundColor: 'rgba(255, 206, 84, 0.8)',
              borderColor: 'rgba(255, 206, 84, 1)',
              borderWidth: 1
            }]
          },
          options: {
            ...this.getChartOptions(),
            plugins: {
              title: {
                display: true,
                text: 'Rank Distribution',
                color: '#e0e0e0'
              },
              legend: { display: false }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: { color: '#e0e0e0' },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
              },
              x: {
                ticks: { 
                  color: '#e0e0e0',
                  maxRotation: 45
                },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
              }
            }
          }
        });
      }
    }

    // Create MMR Distribution Chart (new)
    if (stats.mmrDistribution && stats.mmrDistribution.length > 0) {
      const mmrCanvas = document.getElementById('mmr-chart');
      if (mmrCanvas) {
        // Create readable labels for MMR ranges
        const mmrLabels = stats.mmrDistribution.map(bucket => {
          if (bucket._id === 'other') return 'Other';
          const min = bucket._id;
          const max = bucket._id + 299; // Assuming 300-point ranges
          return `${min}-${max}`;
        });

        this.charts.mmr = this.createChart('mmr-chart', {
          type: 'line',
          data: {
            labels: mmrLabels,
            datasets: [{
              label: 'Players',
              data: stats.mmrDistribution.map(bucket => bucket.count),
              backgroundColor: 'rgba(75, 192, 192, 0.2)',
              borderColor: 'rgba(75, 192, 192, 1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            }]
          },
          options: {
            ...this.getChartOptions(),
            plugins: {
              title: {
                display: true,
                text: 'MMR Distribution',
                color: '#e0e0e0'
              },
              legend: { display: false }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: { color: '#e0e0e0' },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
              },
              x: {
                ticks: { 
                  color: '#e0e0e0',
                  maxRotation: 45
                },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
              }
            }
          }
        });
      }
    }

    // Create Activity Chart (if recent activity data exists)
    if (stats.recentActivity && stats.recentActivity.length > 0) {
      const activityCanvas = document.getElementById('activity-chart');
      if (activityCanvas) {
        this.charts.activity = this.createChart('activity-chart', {
          type: 'line',
          data: {
            labels: stats.recentActivity.map(day => {
              const date = new Date(day.date);
              return date.toLocaleDateString();
            }),
            datasets: [{
              label: 'Daily Matches',
              data: stats.recentActivity.map(day => day.matches),
              backgroundColor: 'rgba(153, 102, 255, 0.2)',
              borderColor: 'rgba(153, 102, 255, 1)',
              borderWidth: 2,
              fill: true
            }]
          },
          options: {
            ...this.getChartOptions(),
            plugins: {
              title: {
                display: true,
                text: 'Match Activity (Last 30 Days)',
                color: '#e0e0e0'
              },
              legend: { display: false }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: { color: '#e0e0e0' },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
              },
              x: {
                ticks: { 
                  color: '#e0e0e0',
                  maxRotation: 45
                },
                grid: { color: 'rgba(255, 255, 255, 0.1)' }
              }
            }
          }
        });
      }
    }
  }

  createChart(canvasId, config) {
    const canvas = document.getElementById(canvasId);
    if (!canvas) {
      console.error(`Canvas element with id "${canvasId}" not found`);
      return null;
    }

    try {
      const chart = new Chart(canvas, config);
      return chart;
    } catch (error) {
      console.error(`Error creating chart ${canvasId}:`, error);
      return null;
    }
  }

  getChartOptions() {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        }
      }
    };
  }

  // Event handlers
  toggleGame() {
    this.currentGame = this.currentGame === 'warcraft2' ? 'warcraft3' : 'warcraft2';
    
    // Update UI
    const titleElement = document.getElementById('current-game-title');
    const toggleText = document.getElementById('toggle-text');
    const reportText = document.getElementById('report-match-text');
    
    if (this.currentGame === 'warcraft2') {
      titleElement.textContent = 'WC II Ladder';
      toggleText.textContent = 'Click to Switch to WC III';
      reportText.textContent = 'REPORT WC II MATCH';
    } else {
      titleElement.textContent = 'WC III Ladder';
      toggleText.textContent = 'Click to Switch to WC II';
      reportText.textContent = 'REPORT WC III MATCH';
    }
    
    // Reload data for new game
    this.loadInitialData();
  }

  showPlayerStats(player) {
    console.log('🎯 Showing player stats for:', player.name);
    
    const modal = document.getElementById('player-stats-modal');
    const playerNameElement = document.getElementById('stats-player-name');
    const contentElement = document.getElementById('player-stats-content');
    
    if (!modal) {
      console.error('❌ Player stats modal not found');
      return;
    }
    
    // Update modal title
    if (playerNameElement) {
      playerNameElement.textContent = player.name;
    }
    
    // Calculate stats for display
    let totalGames = 0;
    let wins = 0;
    let losses = 0;

    if (player.stats) {
      totalGames = player.stats.totalMatches || player.stats.matches || player.stats.gamesPlayed || 0;
      wins = player.stats.wins || 0;
      losses = player.stats.losses || 0;
    } else {
      wins = player.wins || 0;
      losses = player.losses || 0;
      totalGames = wins + losses;
    }

    const winRate = totalGames > 0 ? ((wins / totalGames) * 100).toFixed(1) : '0.0';
    const winRateClass = this.getWinRateClass(parseFloat(winRate));
    
    // Create player stats content
    if (contentElement) {
      contentElement.innerHTML = `
        <div class="player-stats-overview">
          <div class="player-header">
            <div class="player-rank-info">
              <img src="${player.rank?.image || '/assets/img/ranks/unranked.png'}" 
                   alt="${player.rank?.name || 'Unranked'}" 
                   class="player-rank-large"
                   onerror="this.src='/assets/img/ranks/unranked.png'">
              <div>
                <h3>${player.name}</h3>
                <p class="rank-name">${player.rank?.name || 'Unranked'}</p>
                <p class="mmr">MMR: ${player.mmr || 1000}</p>
              </div>
            </div>
          </div>
          
          <div class="stats-grid">
            <div class="stat-card">
              <h4>Total Games</h4>
              <span class="stat-value">${totalGames}</span>
            </div>
            <div class="stat-card">
              <h4>Wins</h4>
              <span class="stat-value wins">${wins}</span>
            </div>
            <div class="stat-card">
              <h4>Losses</h4>
              <span class="stat-value losses">${losses}</span>
            </div>
            <div class="stat-card">
              <h4>Win Rate</h4>
              <span class="stat-value ${winRateClass}">${winRate}%</span>
            </div>
          </div>
          
          <div class="player-details">
            <div class="detail-item">
              <span class="label">Preferred Race:</span>
              <span class="value">${player.preferredRace || 'Unknown'}</span>
            </div>
            <div class="detail-item">
              <span class="label">Game Type:</span>
              <span class="value">${player.gameType?.toUpperCase() || 'Unknown'}</span>
            </div>
            ${player.user ? '<div class="detail-item"><span class="label">Status:</span><span class="value claimed">Claimed Player</span></div>' : '<div class="detail-item"><span class="label">Status:</span><span class="value unclaimed">Unclaimed Player</span></div>'}
          </div>
        </div>
      `;
    }
    
    // Show modal
    modal.style.display = 'flex';
    modal.classList.add('show');
    document.body.style.overflow = 'hidden';
    
    // Setup close functionality
    const closeBtn = modal.querySelector('.close-modal');
    if (closeBtn) {
      closeBtn.onclick = () => this.closePlayerStatsModal();
    }
    
    modal.onclick = (e) => {
      if (e.target === modal) {
        this.closePlayerStatsModal();
      }
    };
  }

  closePlayerStatsModal() {
    const modal = document.getElementById('player-stats-modal');
    if (modal) {
      modal.style.display = 'none';
      modal.classList.remove('show');
      document.body.style.overflow = '';
    }
  }

  searchPlayers() {
    const searchInput = document.getElementById('player-search');
    const newSearchQuery = searchInput?.value.trim() || '';
    
    // If search is empty, reset to show all players
    if (newSearchQuery === '' && this.searchQuery !== '') {
      console.log('🔄 Empty search, resetting to show all players');
      this.searchQuery = '';
      this.currentPage = 1;
      this.loadLeaderboard(this.currentMatchType, this.currentPage, '');
      return;
    }
    
    // Only search if query has changed or is not empty
    if (newSearchQuery !== this.searchQuery) {
      console.log(`🔍 Searching for: "${newSearchQuery}"`);
      this.searchQuery = newSearchQuery;
      this.currentPage = 1;
      this.loadLeaderboard(this.currentMatchType, this.currentPage, this.searchQuery);
    }
  }

  filterByMatchType(matchType) {
    console.log(`🔄 Filtering by match type: ${matchType}`);
    this.currentMatchType = matchType;
    this.currentPage = 1;
    
    // Update active filter button
    document.querySelectorAll('.filter-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.matchType === matchType);
    });
    
    this.loadLeaderboard(this.currentMatchType, this.currentPage, this.searchQuery);
  }

  previousPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
      this.loadLeaderboard(this.currentMatchType, this.currentPage, this.searchQuery);
    }
  }

  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.currentPage++;
      this.loadLeaderboard(this.currentMatchType, this.currentPage, this.searchQuery);
    }
  }

  updatePagination(currentPage, totalPages) {
    this.currentPage = currentPage;
    this.totalPages = totalPages;
    
    const pageInfo = document.getElementById('page-info');
    const prevBtn = document.getElementById('prev-page');
    const nextBtn = document.getElementById('next-page');
    
    if (pageInfo) pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
    if (prevBtn) prevBtn.disabled = currentPage <= 1;
    if (nextBtn) nextBtn.disabled = currentPage >= totalPages;
  }

  openReportModal() {
    const modal = document.getElementById('report-match-modal');
    if (modal) {
      modal.classList.add('show');
      document.body.style.overflow = 'hidden';
    }
  }

  closeModal(modal) {
    if (modal) {
      modal.classList.remove('show');
      document.body.style.overflow = '';
    }
  }

  // Utility methods
  formatDate(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    
    return date.toLocaleDateString();
  }

  // Fallback mock data methods
  loadMockData() {
    console.log('📋 Loading mock data as fallback...');
    this.displayMockRanks();
    this.displayMockLeaderboard();
    this.displayMockMatches();
    this.displayMockStats();
  }

  displayMockRanks() {
    const container = document.getElementById('ranks-container');
    if (container) {
      container.innerHTML = `
        <div class="gentle-message">
          <div class="message-icon">🏆</div>
          <h4>Ranking System</h4>
          <p>Climb through the ranks by winning matches!</p>
          <p>From Bronze to Champion, your skill determines your rank.</p>
          <small>Start playing to see the rank progression here.</small>
        </div>
      `;
    }
  }

  displayMockLeaderboard() {
    const tbody = document.getElementById('leaderboard-body');
    if (tbody) {
      tbody.innerHTML = `
        <tr>
          <td colspan="6" class="gentle-message-cell">
            <div class="gentle-message">
              <div class="message-icon">⚔️</div>
              <h4>Welcome to WC Arena!</h4>
              <p>The leaderboard will show the top players once matches begin.</p>
              <p>Report your first match to join the competition!</p>
              <small>Rankings update in real-time as players compete.</small>
            </div>
          </td>
        </tr>
      `;
    }
  }

  displayMockMatches() {
    const container = document.getElementById('recent-matches-container');
    if (container) {
      container.innerHTML = `
        <div class="gentle-message">
          <div class="message-icon">🎮</div>
          <h4>Recent Matches</h4>
          <p>The latest match results will appear here.</p>
          <p>See how other players are performing and learn from their strategies!</p>
          <small>Match history updates automatically after each game.</small>
        </div>
      `;
    }
  }

  displayMockStats() {
    // Update race stats with gentle message
    const raceList = document.getElementById('race-stats-list');
    if (raceList) {
      raceList.innerHTML = `
        <li class="stats-message">
          <span class="stats-icon">🏰</span>
          <div>
            <div class="stats-title">Race Statistics</div>
            <div class="stats-subtitle">Choose your faction wisely!</div>
          </div>
        </li>
      `;
    }

    // Update mode stats with gentle message
    const modesList = document.getElementById('modes-stats-list');
    if (modesList) {
      modesList.innerHTML = `
        <li class="stats-message">
          <span class="stats-icon">⚔️</span>
          <div>
            <div class="stats-title">Game Modes</div>
            <div class="stats-subtitle">From 1v1 to epic team battles!</div>
          </div>
        </li>
      `;
    }

    // Update map stats with gentle message
    const mapsList = document.getElementById('maps-stats-list');
    if (mapsList) {
      mapsList.innerHTML = `
        <li class="stats-message">
          <span class="stats-icon">🗺️</span>
          <div>
            <div class="stats-title">Popular Maps</div>
            <div class="stats-subtitle">Discover the battlegrounds!</div>
          </div>
        </li>
      `;
    }

    // Update resource stats with gentle message
    const resourcesList = document.getElementById('resources-stats-list');
    if (resourcesList) {
      resourcesList.innerHTML = `
        <li class="stats-message">
          <span class="stats-icon">💎</span>
          <div>
            <div class="stats-title">Resource Levels</div>
            <div class="stats-subtitle">High, Medium, or Low resources</div>
          </div>
        </li>
      `;
    }
  }
}

// Initialize the ladder manager when the page loads
document.addEventListener('DOMContentLoaded', () => {
  window.ladderManager = new LadderManager();
});

// Legacy function support
function initLadderPage() {
  console.log('✅ Legacy initLadderPage called - using new LadderManager');
  return window.ladderManager || new LadderManager();
} 