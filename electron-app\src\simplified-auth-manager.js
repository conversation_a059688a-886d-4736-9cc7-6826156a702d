const { shell, ipcMain } = require('electron');
const axios = require('axios');
const Store = require('electron-store');
const crypto = require('crypto');

class SimplifiedAuthManager {
  constructor(store) {
    this.store = store;
    this.serverUrl = store.get('serverUrl', 'http://127.0.0.1:3001');
    this.pendingOAuth = null;
    this.authTimeout = null;
    
    console.log('🔐 SimplifiedAuthManager initialized');
  }

  updateServerUrl(url) {
    this.serverUrl = url;
    console.log('🔧 Updated server URL:', url);
  }

  setMainWindow(mainWindow) {
    this.mainWindow = mainWindow;
  }

  async loginWithProvider(provider) {
    console.log(`🔐 Starting simplified ${provider} OAuth login`);
    
    try {
      // Clear any pending OAuth
      this.clearPendingOAuth();
      
      // Generate secure state for CSRF protection
      const state = this.generateSecureState();
      this.pendingOAuth = {
        provider,
        state,
        timestamp: Date.now()
      };
      
      // Set timeout for OAuth completion (5 minutes)
      this.authTimeout = setTimeout(() => {
        this.handleOAuthTimeout();
      }, 5 * 60 * 1000);
      
      // Build OAuth URL
      const authUrl = this.buildOAuthUrl(provider, state);
      console.log('🌐 Opening OAuth URL in system browser:', authUrl);
      
      // Open in system default browser
      await shell.openExternal(authUrl);
      
      // Return success - the actual auth completion will be handled via protocol callback
      return { 
        success: true, 
        message: `Opened ${provider} login in your default browser. Please complete the login there.` 
      };
      
    } catch (error) {
      console.error(`❌ ${provider} OAuth error:`, error);
      this.clearPendingOAuth();
      return { success: false, error: error.message };
    }
  }

  buildOAuthUrl(provider, state) {
    // Use port 3001 for OAuth calls since backend is running on port 3001
    const oauthBaseUrl = 'http://127.0.0.1:3001';
    const baseUrl = `${oauthBaseUrl}/auth/${provider}`;
    const params = new URLSearchParams({
      electron: 'true',
      state: state,
      redirect_uri: 'warcraftarena://oauth-success'
    });
    
    return `${baseUrl}?${params.toString()}`;
  }

  generateSecureState() {
    return crypto.randomBytes(32).toString('hex');
  }

  async handleOAuthCallback(url) {
    console.log('🔄 Processing OAuth callback:', url);
    
    try {
      const urlObj = new URL(url);
      const token = urlObj.searchParams.get('token');
      const state = urlObj.searchParams.get('state');
      const error = urlObj.searchParams.get('error');
      
      if (error) {
        throw new Error(`OAuth error: ${error}`);
      }
      
      if (!token) {
        throw new Error('No authentication token received');
      }
      
      // Check if we already have this token to prevent duplicate processing
      const existingToken = this.store.get('authToken');
      if (existingToken === token) {
        console.log('⚠️ Token already processed, skipping duplicate callback');
        return { success: true, token, alreadyProcessed: true };
      }
      
      // Validate state parameter - be more lenient for Electron apps
      if (this.pendingOAuth && this.pendingOAuth.state !== state) {
        console.warn('⚠️ OAuth state mismatch, but continuing for Electron app');
        // Don't throw error for Electron apps, just log warning and continue
        // This is common in Electron apps due to multiple instances and timing issues
      }
      
      // Clear pending OAuth
      this.clearPendingOAuth();
      
      // Store the token
      this.store.set('authToken', token);
      this.store.set('user', null); // Will be fetched when needed
      
      console.log('✅ OAuth completed successfully');
      
      // Notify renderer process of successful login
      if (this.mainWindow) {
        this.mainWindow.webContents.send('oauth-success', {
          message: 'Login successful!'
        });
      }
      
      return { success: true, token };
      
    } catch (error) {
      console.error('❌ OAuth callback error:', error);
      this.clearPendingOAuth();
      
      // Notify renderer process of error
      if (this.mainWindow) {
        this.mainWindow.webContents.send('oauth-error', {
          error: error.message
        });
      }
      
      return { success: false, error: error.message };
    }
  }

  clearPendingOAuth() {
    if (this.authTimeout) {
      clearTimeout(this.authTimeout);
      this.authTimeout = null;
    }
    this.pendingOAuth = null;
  }

  handleOAuthTimeout() {
    console.log('⏰ OAuth timeout - clearing pending state');
    this.clearPendingOAuth();
    
    // Notify renderer process of timeout
    if (this.mainWindow) {
      this.mainWindow.webContents.send('oauth-timeout', {
        message: 'Login timed out. Please try again.'
      });
    }
  }

  async getUserData() {
    try {
      const token = this.store.get('authToken');
      if (!token) {
        return null;
      }
      
      const response = await axios.get(`${this.serverUrl}/api/user/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 10000
      });
      
      if (response.data && response.data.success) {
        const userData = response.data.user;
        this.store.set('user', userData);
        return userData;
      }
      
      return null;
    } catch (error) {
      console.error('❌ Failed to get user data:', error.message);
      
      // If token is invalid, clear it
      if (error.response && error.response.status === 401) {
        this.store.delete('authToken');
        this.store.delete('user');
      }
      
      return null;
    }
  }

  async logout() {
    try {
      const token = this.store.get('authToken');
      
      if (token) {
        // Notify server of logout
        try {
          await axios.post(`${this.serverUrl}/api/auth/logout`, {}, {
            headers: {
              'Authorization': `Bearer ${token}`
            },
            timeout: 5000
          });
        } catch (error) {
          console.warn('⚠️ Server logout failed (continuing with local logout):', error.message);
        }
      }
      
      // Clear local data
      this.store.delete('authToken');
      this.store.delete('user');
      this.clearPendingOAuth();
      
      console.log('✅ Logout completed');
      return { success: true };
      
    } catch (error) {
      console.error('❌ Logout error:', error);
      return { success: false, error: error.message };
    }
  }

  isAuthenticated() {
    const token = this.store.get('authToken');
    return !!token;
  }

  getStoredUser() {
    return this.store.get('user');
  }

  getAuthToken() {
    return this.store.get('authToken');
  }

  async getCurrentUser() {
    try {
      // First try to get cached user data
      let userData = this.store.get('user');
      
      // If no cached data, fetch from server
      if (!userData) {
        userData = await this.getUserData();
      }
      
      return userData;
    } catch (error) {
      console.error('❌ Error getting current user:', error);
      return null;
    }
  }

  async validateStoredUser(storedUser) {
    try {
      const token = this.store.get('authToken');
      if (!token || !storedUser) {
        return false;
      }
      
      // Try to validate the token with the server
      const response = await axios.get(`${this.serverUrl}/api/user/profile`, {
        headers: {
          'Authorization': `Bearer ${token}`
        },
        timeout: 5000
      });
      
      if (response.data && response.data.success && response.data.user) {
        // Update stored user data with fresh data from server
        this.store.set('user', response.data.user);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('❌ Stored user validation failed:', error.message);
      
      // If token is invalid, clear all auth data
      if (error.response && error.response.status === 401) {
        this.store.delete('authToken');
        this.store.delete('user');
      }
      
      return false;
    }
  }

  // Setup IPC handlers for the simplified auth manager
  setupIPC() {
    // Check if handlers are already registered to prevent duplicates
    if (this.ipcHandlersRegistered) {
      console.log('🔐 IPC handlers already registered, skipping...');
      return;
    }

    try {
      ipcMain.handle('auth:login', async (event, provider) => {
        console.log('🔐 Login request received:', provider);
        return await this.loginWithProvider(provider);
      });

      ipcMain.handle('auth:logout', async (event) => {
        console.log('🔐 Logout request received');
        return await this.logout();
      });

      ipcMain.handle('auth:get-user', async (event) => {
        console.log('👤 Get user request received');
        const userData = await this.getUserData();
        return { success: true, user: userData };
      });

      ipcMain.handle('auth:is-authenticated', async (event) => {
        return { authenticated: this.isAuthenticated() };
      });

      ipcMain.handle('auth:get-current-user', async (event) => {
        console.log('👤 Get current user request received');
        const userData = await this.getCurrentUser();
        return { success: true, user: userData };
      });

      ipcMain.handle('auth:get-token', async (event) => {
        console.log('🔑 Get token request received');
        const token = this.getAuthToken();
        return { success: true, token };
      });

      this.ipcHandlersRegistered = true;
      console.log('✅ Auth IPC handlers registered successfully');
    } catch (error) {
      console.error('❌ Error registering auth IPC handlers:', error);
    }
  }
}

module.exports = { SimplifiedAuthManager }; 