/**
 * AuthManager - <PERSON>les authentication state and token management
 */
export default class AuthManager {
  static instance = null;

  constructor() {
    if (AuthManager.instance) {
      return AuthManager.instance;
    }
    AuthManager.instance = this;

    this.isElectron = this.checkIsElectron();
    this.authToken = null;
    this.user = null;
    this.initialized = false;
    
    // Set up event listeners
    this.setupEventListeners();
  }

  static getInstance() {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }

  static async init() {
    const instance = AuthManager.getInstance();
    if (!instance.initialized) {
      await instance.initialize();
    }
    return instance;
  }

  checkIsElectron() {
    return typeof window !== 'undefined' && 
           (window.electronAPI || 
            window.require || 
            navigator.userAgent.toLowerCase().indexOf('electron') > -1);
  }

  setupEventListeners() {
    if (this.isElectron && window.electronAPI) {
      // Listen for auth messages from Electron
      window.addEventListener('message', (event) => {
        if (event.data.type === 'ELECTRON_AUTH') {
          this.handleElectronAuth(event.data.token);
        }
      });
    }
  }

  async initialize() {
    try {
      // Check for stored token
      const storedToken = localStorage.getItem('authToken');
      if (storedToken) {
        this.authToken = storedToken;
      }

      // Try to get user data
      await this.fetchUserData();
      
      this.initialized = true;
      console.log('✅ AuthManager initialized');
    } catch (error) {
      console.error('❌ AuthManager initialization failed:', error);
      this.initialized = true; // Still mark as initialized
    }
  }

  handleElectronAuth(token) {
    if (token) {
      this.authToken = token;
      localStorage.setItem('authToken', token);
      this.fetchUserData();
    }
  }

  async fetchUserData() {
    try {
      const headers = {
        'Content-Type': 'application/json'
      };
      
      if (this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }

      const response = await fetch('/api/me', { headers });
      
      if (response.ok) {
        const userData = await response.json();
        this.user = userData;
        console.log('✅ User data loaded:', userData.username);
        this.dispatchAuthEvent('login', userData);
        return userData;
      } else if (response.status === 401) {
        console.log('❌ User not authenticated');
        this.clearAuth();
        return null;
      } else {
        throw new Error(`API request failed: ${response.status}`);
      }
    } catch (error) {
      console.error('❌ Failed to fetch user data:', error);
      return null;
    }
  }

  clearAuth() {
    this.authToken = null;
    this.user = null;
    localStorage.removeItem('authToken');
    this.dispatchAuthEvent('logout', null);
  }

  dispatchAuthEvent(type, data) {
    window.dispatchEvent(new CustomEvent('authStateChange', {
      detail: { type, data, user: this.user, isAuthenticated: !!this.user }
    }));
  }

  isAuthenticated() {
    return !!this.user;
  }

  getUser() {
    return this.user;
  }

  getToken() {
    return this.authToken;
  }
}

// Auto-initialize when imported
if (typeof window !== 'undefined') {
  AuthManager.init();
} 