<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Arena Core - WC Arena</title>
  
  <!-- Universal Navbar - MUST be loaded first -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
  <link rel="stylesheet" href="/css/navbar-dropdown.css" />
  
  <!-- Page-specific styles -->
  <link rel="stylesheet" href="http://127.0.0.1:3000/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="http://127.0.0.1:3000/css/profile-specific.css" />
  <link rel="stylesheet" href="http://127.0.0.1:3000/css/game-manager.css" />
  <link rel="stylesheet" href="http://127.0.0.1:3000/css/components/notifications.css" />
  <link rel="stylesheet" href="http://127.0.0.1:3000/css/matches-tab-enhanced.css" />
  <link rel="stylesheet" href="http://127.0.0.1:3000/css/player-modal-enhanced.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <!-- Navigation will be loaded here -->
  <nav id="navbar-container"></nav>
  
  <!-- Main Content -->
  <main class="game-manager-main">
    <div class="container">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title" data-theme="arenacore">⚙️ Arena Core</h1>
        <div class="header-content">
          <p class="page-subtitle">Manage your games, screenshots, and match tracking</p>
        </div>
        <div class="mode-indicator">
          <div class="mode-badge web-mode" id="current-mode">
            <i class="fas fa-globe"></i>
            <span>Web Browser</span>
          </div>
        </div>
      </div>

      <!-- Game Manager Dashboard (Desktop App Only) -->
      <div class="game-manager-dashboard electron-only">
        
        <!-- Quick Stats -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-gamepad"></i>
            </div>
            <div class="stat-content">
              <h3 class="stat-number" id="games-detected">0</h3>
              <p class="stat-label">Games Detected</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-camera"></i>
            </div>
            <div class="stat-content">
              <h3 class="stat-number" id="screenshots-today">0</h3>
              <p class="stat-label">Screenshots Today</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-trophy"></i>
            </div>
            <div class="stat-content">
              <h3 class="stat-number" id="matches-tracked">0</h3>
              <p class="stat-label">Matches Tracked</p>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
              <h3 class="stat-number" id="play-time">0h</h3>
              <p class="stat-label">Play Time Today</p>
            </div>
          </div>
        </div>

        <!-- Main Content Grid -->
        <div class="content-grid">
          
          <!-- Game Detection & Launcher Panel -->
          <div class="panel game-detection-panel">
            <div class="panel-header">
              <h2 class="panel-title">
                <i class="fas fa-cogs"></i>
                Game Launcher & Detection
              </h2>
              <div class="panel-actions">
                <button class="btn btn-secondary" id="add-game-manual">
                  <i class="fas fa-plus"></i>
                  Add Game
                </button>
                <button class="btn btn-secondary" id="force-refresh-games" title="Perform a fresh scan and update system tray">
                  <i class="fas fa-redo"></i>
                  Force Refresh
                </button>
                <button class="btn btn-primary" id="scan-games" title="Show currently detected games (same as tray launcher)">
                  <i class="fas fa-sync"></i>
                  Show Games
                </button>
              </div>
            </div>
            
            <!-- Game Filter Tabs -->
            <div class="game-filter-tabs">
              <button class="game-filter-tab active" data-filter="all">
                <i class="fas fa-list"></i>
                <span>All Games</span>
              </button>
              <button class="game-filter-tab" data-filter="warcraft1">
                <i class="fas fa-castle"></i>
                <span>WC1</span>
              </button>
              <button class="game-filter-tab" data-filter="warcraft2">
                <i class="fas fa-ship"></i>
                <span>WC2</span>
              </button>
              <button class="game-filter-tab" data-filter="warcraft3">
                <i class="fas fa-tree"></i>
                <span>WC3</span>
              </button>
            </div>
            
            <div class="panel-content">
              <div class="game-list-container">
                <div class="game-list" id="detected-games">
                <!-- Games will be populated here -->
                <div class="no-games-message">
                  <i class="fas fa-search"></i>
                  <p>No games detected yet</p>
                  <small>Click "Show Games" to see detected games or "Force Refresh" to scan for new games</small>
                </div>
              </div>
              </div>
              
              <!-- Add Game Form (hidden by default) -->
              <div class="add-game-form" id="add-game-form" style="display: none;">
                <h4>Add Game Manually</h4>
                <form id="manual-game-form">
                  <div class="form-row">
                    <label>Game Name:</label>
                    <input type="text" id="game-name" placeholder="e.g., Warcraft II" required>
                  </div>
                  <div class="form-row">
                    <label>Game Type:</label>
                    <select id="game-type" required>
                      <option value="">Select Type</option>
                      <option value="warcraft1">Warcraft I: Orcs & Humans</option>
                      <option value="warcraft2">Warcraft II: Tides of Darkness</option>
                      <option value="warcraft3">Warcraft III</option>
                      <option value="w3champions">W3Champions</option>
                      <option value="battlenet">Battle.net</option>
                    </select>
                  </div>
                  <div class="form-row">
                    <label>Executable Path:</label>
                    <div class="file-input-group">
                      <input type="text" id="game-path" placeholder="Path to game executable" required>
                      <button type="button" class="btn btn-small" id="browse-executable">
                        <i class="fas fa-folder"></i>
                        Browse
                      </button>
                    </div>
                  </div>
                  <div class="form-row">
                    <label>Arguments (Optional):</label>
                    <input type="text" id="game-args" placeholder="e.g., -windowed -novideo">
                  </div>
                  <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                      <i class="fas fa-save"></i>
                      Add Game
                    </button>
                    <button type="button" class="btn btn-secondary" id="cancel-add-game">
                      <i class="fas fa-times"></i>
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Screenshot Manager Section -->
          <div class="manager-section">
            <div class="section-header">
              <h3><i class="fas fa-camera"></i> Screenshot Detection</h3>
              <p>Automatically detects and analyzes your in-game screenshots</p>
            </div>
            
            <div class="screenshot-info">
              <div class="info-card">
                <h4><i class="fas fa-info-circle"></i> How It Works</h4>
                <ul>
                  <li>Take screenshots in-game using your system screenshot key</li>
                  <li>Screenshots are automatically detected and organized by game</li>
                  <li>AI analyzes screenshots to detect victory/defeat results</li>
                  <li>Results can be reported to the ladder system</li>
                </ul>
              </div>
              
              <div class="screenshot-actions">
                <button id="open-screenshot-folder" class="btn btn-primary">
                  <i class="fas fa-folder-open"></i> Open Screenshot Folder
                </button>
                
                <div class="screenshot-settings">
                  <label class="checkbox-label">
                    <input type="checkbox" id="auto-analysis" checked>
                    <span>Auto-analyze screenshots for match results</span>
                  </label>
                  
                  <label class="checkbox-label">
                    <input type="checkbox" id="auto-report" checked>
                    <span>Auto-report high-confidence results to ladder</span>
                  </label>
                </div>
              </div>
            </div>
            
            <div class="recent-screenshots">
              <h4>Recent Screenshots</h4>
              <div id="screenshots-list" class="screenshots-grid">
                <!-- Screenshots will be populated here -->
              </div>
            </div>
          </div>

          <!-- Match Tracking -->
          <div class="panel match-tracking-panel">
            <div class="panel-header">
              <h2 class="panel-title">
                <i class="fas fa-trophy"></i>
                Match Tracking
              </h2>
              <div class="tracking-toggle">
                <input type="checkbox" id="auto-tracking" class="toggle-checkbox">
                <label for="auto-tracking" class="toggle-label">
                  <span class="toggle-inner"></span>
                  <span class="toggle-switch"></span>
                </label>
                <span class="toggle-text">Auto-track matches</span>
              </div>
            </div>
            <div class="panel-content">
              <div class="match-list" id="recent-matches">
                <!-- Matches will be populated here -->
                <div class="no-matches-message">
                  <i class="fas fa-trophy"></i>
                  <p>No matches tracked yet</p>
                  <small>Enable auto-tracking to automatically detect matches</small>
                </div>
              </div>
            </div>
          </div>

          <!-- App Controls (Electron Only) -->
          <div class="panel app-controls-panel electron-only">
            <div class="panel-header">
              <h2 class="panel-title">
                <i class="fas fa-desktop"></i>
                App Controls
              </h2>
            </div>
            <div class="panel-content">
              <div class="app-controls-grid">
                <button class="app-control-btn" id="switch-to-launcher">
                  <i class="fas fa-cogs"></i>
                  <span>Game Launcher Mode</span>
                  <small>Switch to dedicated game launcher</small>
                </button>
                <button class="app-control-btn" id="refresh-website">
                  <i class="fas fa-sync"></i>
                  <span>Refresh Website</span>
                  <small>Reload the website content</small>
                </button>
                <button class="app-control-btn" id="minimize-to-tray">
                  <i class="fas fa-window-minimize"></i>
                  <span>Minimize to Tray</span>
                  <small>Hide app in system tray</small>
                </button>
                <button class="app-control-btn danger" id="logout-app">
                  <i class="fas fa-sign-out-alt"></i>
                  <span>Logout</span>
                  <small>Sign out from the app</small>
                </button>
              </div>
            </div>
          </div>

          <!-- Settings & Configuration -->
          <div class="panel settings-panel">
            <div class="panel-header">
              <h2 class="panel-title">
                <i class="fas fa-cog"></i>
                Settings
              </h2>
            </div>
            <div class="panel-content">
              <div class="settings-grid">
                
                <div class="setting-group">
                  <label class="setting-label">
                    <i class="fas fa-camera"></i>
                    Screenshot Quality
                  </label>
                  <select class="setting-input" id="screenshot-quality">
                    <option value="high">High Quality</option>
                    <option value="medium" selected>Medium Quality</option>
                    <option value="low">Low Quality</option>
                  </select>
                </div>

                <div class="setting-group">
                  <label class="setting-label">
                    <i class="fas fa-folder"></i>
                    Screenshot Folder
                  </label>
                  <div class="folder-input">
                    <input type="text" class="setting-input" id="screenshot-folder" readonly>
                    <button class="btn btn-small" id="browse-folder">
                      <i class="fas fa-folder-open"></i>
                      Browse
                    </button>
                  </div>
                </div>

                <div class="setting-group">
                  <label class="setting-label">
                    <i class="fas fa-clock"></i>
                    Scan Frequency
                  </label>
                  <select class="setting-input" id="scan-frequency">
                    <option value="30">30 seconds</option>
                    <option value="60" selected>1 minute</option>
                    <option value="300">5 minutes</option>
                    <option value="0">Manual only</option>
                  </select>
                </div>

                <div class="setting-group">
                  <label class="setting-label">
                    <i class="fas fa-bell"></i>
                    Notifications
                  </label>
                  <div class="checkbox-group">
                    <label class="checkbox-label">
                      <input type="checkbox" id="notify-game-start" checked>
                      <span class="checkmark"></span>
                      Game start/end
                    </label>
                    <label class="checkbox-label">
                      <input type="checkbox" id="notify-screenshot" checked>
                      <span class="checkmark"></span>
                      Screenshot taken
                    </label>
                    <label class="checkbox-label">
                      <input type="checkbox" id="notify-match-end" checked>
                      <span class="checkmark"></span>
                      Match completed
                    </label>
                  </div>
                </div>

                <div class="setting-group electron-only">
                  <label class="setting-label">
                    <i class="fas fa-cog"></i>
                    App Behavior
                  </label>
                  <div class="checkbox-group">
                    <label class="checkbox-label">
                      <input type="checkbox" id="minimize-to-tray" checked>
                      <span class="checkmark"></span>
                      Minimize to system tray
                    </label>
                    <label class="checkbox-label">
                      <input type="checkbox" id="close-to-tray" checked>
                      <span class="checkmark"></span>
                      Close to system tray
                    </label>
                    <label class="checkbox-label">
                      <input type="checkbox" id="auto-find-games" checked>
                      <span class="checkmark"></span>
                      Auto-detect games on startup
                    </label>
                  </div>
                </div>

              </div>
            </div>
          </div>

        </div>
      </div>

      <!-- Electron App Features (only visible in Electron) -->
      <div class="electron-features" id="electron-features">
        <div class="feature-banner">
          <div class="banner-content">
            <div class="banner-icon">
              <i class="fas fa-desktop"></i>
            </div>
            <div class="banner-text">
              <h3>Desktop App Features</h3>
              <p>You're using the desktop app! All game management features are available.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Web Version Notice (only visible in browser) -->
      <div class="web-notice" id="web-notice">
        <div class="notice-banner">
          <div class="banner-content">
            <div class="banner-icon">
              <i class="fas fa-globe"></i>
            </div>
            <div class="banner-text">
              <h3>Web Version Limitations</h3>
              <p>Some features require the desktop app for full functionality.</p>
            </div>
            <div class="banner-actions">
              <a href="/download" class="btn btn-primary">
                <i class="fas fa-download"></i>
                Download Desktop App
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Web Browser Download Prompt -->
      <div class="download-prompt web-only">
        <div class="download-hero">
          <div class="download-icon">
            <i class="fas fa-download"></i>
          </div>
          <h2>Arena Core Requires Desktop App</h2>
          <p>Access powerful game management features with the WC Arena Companion desktop app</p>
        </div>

        <div class="features-showcase">
          <div class="feature-grid">
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-cogs"></i>
              </div>
              <h3>Auto Game Detection</h3>
              <p>Automatically detect and launch Warcraft I, II, and III games</p>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-camera"></i>
              </div>
              <h3>Screenshot Manager</h3>
              <p>Capture and organize your best gaming moments</p>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-trophy"></i>
              </div>
              <h3>Match Tracking</h3>
              <p>Automatically track and analyze your matches</p>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <h3>Performance Analytics</h3>
              <p>Detailed statistics and performance insights</p>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-bolt"></i>
              </div>
              <h3>System Tray Integration</h3>
              <p>Quick access to all features from your system tray</p>
            </div>
            
            <div class="feature-item">
              <div class="feature-icon">
                <i class="fas fa-sync"></i>
              </div>
              <h3>Real-time Sync</h3>
              <p>Seamless sync between desktop app and website</p>
            </div>
          </div>
        </div>

        <div class="download-actions">
          <a href="/views/download.html" class="download-btn primary">
            <i class="fas fa-download"></i>
            <span>Download Desktop App</span>
            <small>Free • Windows, Mac, Linux</small>
          </a>
          
          <div class="download-info">
            <p><strong>Already have the app?</strong></p>
            <p>Make sure you're accessing this page through the WC Arena Companion desktop application to use Arena Core features.</p>
          </div>
        </div>
      </div>

    </div>
  </main>

  <!-- Footer will be loaded here -->
  <footer id="footer-container"></footer>

  <!-- Scripts -->
  <script src="/js/main.js"></script>
  <script src="/js/game-manager-page.js"></script>
  
  <script>
    // Initialize unified navigation system
    window.addEventListener('load', async () => {
      console.log('🔄 Initializing navigation on game-manager page...');

      // Load unified navigation
      if (typeof window.loadNavigation === 'function') {
        await window.loadNavigation();
      } else if (typeof window.loadNavbar === 'function') {
        await window.loadNavbar();
      }

      setTimeout(async () => {
        if (window.updateNavbarProfileUnified) {
          console.log('🔄 Updating navbar profile (unified) on game-manager page');
          await window.updateNavbarProfileUnified();
        } else if (window.updateNavbarProfile) {
          console.log('🔄 Updating navbar profile (legacy) on game-manager page');
          await window.updateNavbarProfile();
        }
      }, 500);
    });
  </script>
</body>
</html> 
