const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

class ImageAnalyzer {
  constructor() {
    this.isAnalyzing = false;
    
    // Enhanced victory/defeat text patterns for different Warcraft games
    this.victoryPatterns = [
      // Warcraft 1
      /victory\b/i, /you have won\b/i, /mission accomplished\b/i, /conquest complete\b/i,
      // Warcraft 2
      /victory!\b/i, /mission completed\b/i, /you are victorious\b/i, /conquest achieved\b/i,
      // Warcraft 3
      /victory\b/i, /mission accomplished\b/i, /you win\b/i, /defeat\b/i,
      /your forces have won\b/i, /you are victorious\b/i, /triumph\b/i
    ];
    
    this.defeatPatterns = [
      // General defeat patterns
      /defeat\b/i, /you have been defeated\b/i, /mission failed\b/i,
      /game over\b/i, /you lose\b/i, /your forces have been defeated\b/i,
      /you have lost\b/i, /defeat!\b/i, /mission failure\b/i, /eliminated\b/i
    ];

    // Player name patterns for different games
    this.playerPatterns = {
      warcraft1: [
        /player\s+(\d+):\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /human:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /orc:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi
      ],
      warcraft2: [
        /player\s+(\d+):\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /human:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /orc:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /alliance:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /horde:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi
      ],
      warcraft3: [
        /player\s+(\d+):\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /human:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /orc:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /undead:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /night\s+elf:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /random:\s*([a-zA-Z0-9_\-\[\]]{2,16})/gi,
        /(\w+)\s+has\s+been\s+defeated/gi,
        /(\w+)\s+is\s+victorious/gi
      ]
    };

    // Map name patterns
    this.mapPatterns = [
      // Common map patterns
      /map:\s*([^,\n\r]{3,30})/gi,
      /level:\s*([^,\n\r]{3,30})/gi,
      /scenario:\s*([^,\n\r]{3,30})/gi,
      // Specific map names
      /forest\s+moon/gi, /nowhere\s+to\s+run/gi, /crossroads/gi,
      /garden\s+of\s+war/gi, /gold\s+rush/gi, /lost\s+temple/gi,
      /turtle\s+rock/gi, /echo\s+isles/gi, /terenas\s+stand/gi
    ];

    // Match duration patterns
    this.durationPatterns = [
      /duration:\s*(\d{1,2}):(\d{2})/gi,
      /time:\s*(\d{1,2}):(\d{2})/gi,
      /game\s+time:\s*(\d{1,2}):(\d{2})/gi,
      /match\s+length:\s*(\d{1,2}):(\d{2})/gi,
      /(\d{1,2}):(\d{2})\s+elapsed/gi
    ];
    
    // Color patterns that often indicate victory (gold, green) vs defeat (red)
    this.victoryColors = [
      { r: 255, g: 215, b: 0 },   // Gold
      { r: 0, g: 255, b: 0 },     // Green
      { r: 255, g: 255, b: 0 },   // Yellow
      { r: 50, g: 205, b: 50 },   // Lime green
    ];
    
    this.defeatColors = [
      { r: 255, g: 0, b: 0 },     // Red
      { r: 139, g: 0, b: 0 },     // Dark red
      { r: 255, g: 69, b: 69 },   // Light red
      { r: 220, g: 20, b: 60 },   // Crimson
    ];

    // UI region detection for focused OCR
    this.uiRegions = {
      warcraft1: {
        playerArea: { x: 0.1, y: 0.7, width: 0.8, height: 0.2 },
        resultArea: { x: 0.3, y: 0.4, width: 0.4, height: 0.2 },
        mapArea: { x: 0.0, y: 0.0, width: 1.0, height: 0.15 }
      },
      warcraft2: {
        playerArea: { x: 0.1, y: 0.75, width: 0.8, height: 0.2 },
        resultArea: { x: 0.25, y: 0.35, width: 0.5, height: 0.3 },
        mapArea: { x: 0.0, y: 0.0, width: 1.0, height: 0.15 }
      },
      warcraft3: {
        playerArea: { x: 0.1, y: 0.6, width: 0.8, height: 0.35 },
        resultArea: { x: 0.2, y: 0.3, width: 0.6, height: 0.4 },
        mapArea: { x: 0.0, y: 0.0, width: 1.0, height: 0.15 }
      }
    };
  }

  async analyzeScreenshot(imagePath, gameType) {
    if (this.isAnalyzing) {
      console.log('🔄 Image analysis already in progress, queuing...');
      await this.waitForAnalysisComplete();
    }
    
    this.isAnalyzing = true;
    
    try {
      console.log(`🔍 Starting comprehensive analysis: ${imagePath} (${gameType})`);
      
      const result = {
        imagePath,
        gameType,
        isGameResult: false,
        result: null, // 'victory', 'defeat', or null
        confidence: 0,
        players: [],
        mapName: null,
        duration: null,
        analysis: {
          textFound: [],
          playersDetected: [],
          mapDetected: null,
          durationDetected: null,
          colorAnalysis: {},
          regions: {},
          timestamp: new Date().toISOString(),
          methods: []
        }
      };
      
      // Step 1: Basic file validation
      const imageExists = await this.fileExists(imagePath);
      if (!imageExists) {
        console.log('❌ Image file does not exist');
        return result;
      }

      // Step 2: Enhanced OCR text extraction with region-based analysis
      try {
        const comprehensiveTextAnalysis = await this.performComprehensiveTextAnalysis(imagePath, gameType);
        result.analysis.textFound = comprehensiveTextAnalysis.allWords;
        result.analysis.regions = comprehensiveTextAnalysis.regions;
        result.analysis.methods.push('comprehensive_ocr');
        
        // Analyze for game result
        const textResult = this.analyzeTextForGameResult(comprehensiveTextAnalysis.allWords);
        if (textResult.found) {
          result.isGameResult = true;
          result.result = textResult.result;
          result.confidence = Math.max(result.confidence, textResult.confidence);
          result.analysis.methods.push('text_result_detection');
        }

        // Extract player information
        const playerResult = this.extractPlayersFromText(comprehensiveTextAnalysis, gameType);
        if (playerResult.players.length > 0) {
          result.players = playerResult.players;
          result.analysis.playersDetected = playerResult.players;
          result.confidence = Math.max(result.confidence, playerResult.confidence);
          result.analysis.methods.push('player_detection');
        }

        // Extract map information
        const mapResult = this.extractMapFromText(comprehensiveTextAnalysis.allWords);
        if (mapResult.mapName) {
          result.mapName = mapResult.mapName;
          result.analysis.mapDetected = mapResult.mapName;
          result.confidence = Math.max(result.confidence, mapResult.confidence);
          result.analysis.methods.push('map_detection');
        }

        // Extract duration information
        const durationResult = this.extractDurationFromText(comprehensiveTextAnalysis.allWords);
        if (durationResult.duration) {
          result.duration = durationResult.duration;
          result.analysis.durationDetected = durationResult.duration;
          result.confidence = Math.max(result.confidence, durationResult.confidence);
          result.analysis.methods.push('duration_detection');
        }

      } catch (error) {
        console.log('⚠️ OCR analysis failed, falling back to color analysis:', error.message);
        result.analysis.methods.push('ocr_failed');
      }
      
      // Step 3: Enhanced color analysis
      if (result.confidence < 70) {
        try {
          const colorAnalysis = await this.performAdvancedColorAnalysis(imagePath);
          result.analysis.colorAnalysis = colorAnalysis;
          
          const colorResult = this.analyzeColorsForGameResult(colorAnalysis);
          if (colorResult.found) {
            result.isGameResult = true;
            result.result = colorResult.result;
            result.confidence = Math.max(result.confidence, colorResult.confidence);
            result.analysis.methods.push('color_analysis');
          }
        } catch (error) {
          console.log('⚠️ Color analysis failed:', error.message);
        }
      }

      // Step 4: Game-specific heuristics and pattern matching
      if (result.confidence < 60) {
        const heuristicResult = this.applyAdvancedGameHeuristics(imagePath, gameType, result.analysis);
        if (heuristicResult.found) {
          result.isGameResult = true;
          result.result = heuristicResult.result;
          result.confidence = Math.max(result.confidence, heuristicResult.confidence);
          result.analysis.methods.push('advanced_heuristics');
        }
      }

      // Step 5: Machine learning-style confidence boosting
      result.confidence = this.calculateFinalConfidence(result);

      if (result.isGameResult) {
        console.log(`✅ Comprehensive analysis complete: ${result.result} with ${result.confidence}% confidence`);
        console.log(`🎯 Methods used: ${result.analysis.methods.join(', ')}`);
        if (result.players.length > 0) {
          console.log(`👥 Players detected: ${result.players.map(p => p.name).join(', ')}`);
        }
        if (result.mapName) {
          console.log(`🗺️ Map detected: ${result.mapName}`);
        }
        if (result.duration) {
          console.log(`⏱️ Duration detected: ${result.duration}`);
        }
      } else {
        console.log('ℹ️ No game result detected after comprehensive analysis');
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ Error in comprehensive screenshot analysis:', error);
      return null;
    } finally {
      this.isAnalyzing = false;
    }
  }

  async performComprehensiveTextAnalysis(imagePath, gameType) {
    const regions = this.uiRegions[gameType] || this.uiRegions.warcraft3;
    const analysisResult = {
      allWords: [],
      regions: {}
    };

    // Full image OCR
    try {
      const fullImageText = await this.extractTextFromImage(imagePath);
      analysisResult.allWords = fullImageText.words;
      analysisResult.regions.fullImage = fullImageText;
    } catch (error) {
      console.log('⚠️ Full image OCR failed:', error.message);
    }

    // Region-specific OCR for better accuracy
    for (const [regionName, region] of Object.entries(regions)) {
      try {
        const regionText = await this.extractTextFromRegion(imagePath, region);
        analysisResult.regions[regionName] = regionText;
        
        // Add region-specific words to main word list
        if (regionText.words) {
          analysisResult.allWords.push(...regionText.words);
        }
      } catch (error) {
        console.log(`⚠️ Region ${regionName} OCR failed:`, error.message);
      }
    }

    // Remove duplicates and clean up
    analysisResult.allWords = [...new Set(analysisResult.allWords)]
      .filter(word => word && word.length > 1)
      .map(word => word.toLowerCase().replace(/[^\w]/g, ''));

    return analysisResult;
  }

  async extractTextFromRegion(imagePath, region) {
    // This would require image cropping functionality
    // For now, return full image text as fallback
    return await this.extractTextFromImage(imagePath);
  }

  extractPlayersFromText(textAnalysis, gameType) {
    const patterns = this.playerPatterns[gameType] || this.playerPatterns.warcraft3;
    const players = [];
    const allText = textAnalysis.allWords.join(' ');

    for (const pattern of patterns) {
      let match;
      pattern.lastIndex = 0; // Reset regex
      
      while ((match = pattern.exec(allText)) !== null) {
        const playerName = match[2] || match[1];
        if (playerName && playerName.length >= 2 && playerName.length <= 16) {
          // Validate player name (basic heuristics)
          if (this.isValidPlayerName(playerName)) {
            players.push({
              name: playerName,
              team: match[1] ? parseInt(match[1]) : null,
              source: 'ocr_extraction'
            });
          }
        }
      }
    }

    // Remove duplicates
    const uniquePlayers = players.filter((player, index, self) => 
      index === self.findIndex(p => p.name.toLowerCase() === player.name.toLowerCase())
    );

    return {
      players: uniquePlayers,
      confidence: uniquePlayers.length > 0 ? Math.min(90, 60 + uniquePlayers.length * 15) : 0
    };
  }

  isValidPlayerName(name) {
    // Basic validation for player names
    if (name.length < 2 || name.length > 16) return false;
    
    // Common false positives to exclude
    const excludeList = [
      'player', 'human', 'orc', 'undead', 'night', 'elf', 'random', 
      'victory', 'defeat', 'mission', 'time', 'score', 'level',
      'map', 'game', 'match', 'duration', 'result'
    ];
    
    return !excludeList.includes(name.toLowerCase());
  }

  extractMapFromText(words) {
    const allText = words.join(' ');
    
    for (const pattern of this.mapPatterns) {
      pattern.lastIndex = 0;
      const match = pattern.exec(allText);
      if (match) {
        const mapName = match[1].trim();
        if (mapName.length >= 3 && mapName.length <= 30) {
          return {
            mapName: mapName,
            confidence: 75
          };
        }
      }
    }

    return { mapName: null, confidence: 0 };
  }

  extractDurationFromText(words) {
    const allText = words.join(' ');
    
    for (const pattern of this.durationPatterns) {
      pattern.lastIndex = 0;
      const match = pattern.exec(allText);
      if (match) {
        const minutes = parseInt(match[1]);
        const seconds = parseInt(match[2]);
        
        if (minutes >= 0 && minutes <= 120 && seconds >= 0 && seconds <= 59) {
          return {
            duration: `${minutes}:${seconds.toString().padStart(2, '0')}`,
            confidence: 80
          };
        }
      }
    }

    return { duration: null, confidence: 0 };
  }

  async performAdvancedColorAnalysis(imagePath) {
    // Enhanced color analysis would go here
    // For now, return basic analysis
    return await this.analyzeImageColors(imagePath);
  }

  applyAdvancedGameHeuristics(imagePath, gameType, analysisData) {
    // Advanced game-specific pattern matching
    let confidence = 0;
    let result = null;

    // Check for game-specific UI elements, colors, and patterns
    if (gameType === 'warcraft3') {
      // WC3-specific heuristics
      if (analysisData.regions && analysisData.regions.resultArea) {
        // Look for specific WC3 end-game patterns
        confidence += 20;
      }
    }

    // File timestamp heuristics (recent files more likely to be game results)
    try {
      const stats = require('fs').statSync(imagePath);
      const ageMinutes = (Date.now() - stats.mtime.getTime()) / (1000 * 60);
      if (ageMinutes < 30) {
        confidence += 15; // Recent screenshots more likely to be game results
      }
    } catch (error) {
      // Ignore file stat errors
    }

    return {
      found: confidence > 50,
      result: result || 'unknown',
      confidence: Math.min(confidence, 70) // Cap heuristic confidence
    };
  }

  calculateFinalConfidence(result) {
    let finalConfidence = result.confidence;

    // Boost confidence for multiple detection methods
    if (result.analysis.methods.length > 1) {
      finalConfidence += result.analysis.methods.length * 5;
    }

    // Boost confidence for additional data detected
    if (result.players.length > 0) finalConfidence += 10;
    if (result.mapName) finalConfidence += 5;
    if (result.duration) finalConfidence += 5;

    // Penalize if no clear result
    if (!result.isGameResult) {
      finalConfidence = Math.max(0, finalConfidence - 20);
    }

    return Math.min(95, Math.max(0, finalConfidence));
  }

  async waitForAnalysisComplete() {
    while (this.isAnalyzing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  async extractTextFromImage(imagePath) {
    // Try to use Tesseract OCR if available
    try {
      // Check if tesseract is available
      await execAsync('tesseract --version');
      
      // Run OCR on the image
      const tempTextFile = path.join(path.dirname(imagePath), 'temp_ocr_output');
      await execAsync(`tesseract "${imagePath}" "${tempTextFile}" -l eng`);
      
      // Read the OCR result
      const ocrText = await fs.readFile(tempTextFile + '.txt', 'utf8');
      
      // Clean up temp file
      try {
        await fs.unlink(tempTextFile + '.txt');
      } catch (e) {
        // Ignore cleanup errors
      }
      
      // Parse words from OCR text
      const words = ocrText.toLowerCase()
        .split(/\s+/)
        .filter(word => word.length > 2)
        .map(word => word.replace(/[^\w]/g, ''));
      
      return { words, rawText: ocrText };
      
    } catch (error) {
      // Tesseract not available, return empty result
      return { words: [], rawText: '' };
    }
  }
  
  analyzeTextForGameResult(words) {
    if (!words || words.length === 0) {
      return { found: false, result: null, confidence: 0 };
    }
    
    const fullText = words.join(' ').toLowerCase();
    let confidence = 0;
    let result = null;
    let maxConfidence = 0;
    
    // Check for victory patterns
    for (const pattern of this.victoryPatterns) {
      if (pattern.test(fullText)) {
        const patternConfidence = 85;
        if (patternConfidence > maxConfidence) {
          maxConfidence = patternConfidence;
          result = 'victory';
          confidence = patternConfidence;
        }
      }
    }
    
    // Check for defeat patterns
    for (const pattern of this.defeatPatterns) {
      if (pattern.test(fullText)) {
        const patternConfidence = 85;
        if (patternConfidence > maxConfidence) {
          maxConfidence = patternConfidence;
          result = 'defeat';
          confidence = patternConfidence;
        }
      }
    }
    
    // Additional context-based confidence adjustments
    if (result) {
      // Look for supporting keywords
      const supportingWords = ['mission', 'game', 'match', 'battle', 'war', 'complete', 'over', 'end'];
      const supportingCount = supportingWords.filter(word => fullText.includes(word)).length;
      confidence += supportingCount * 3;
      
      // Check for time indicators (often in game result screens)
      if (/\d{1,2}:\d{2}/.test(fullText)) {
        confidence += 10;
      }
      
      // Check for score/stats indicators
      if (/score|points|kills|deaths/.test(fullText)) {
        confidence += 8;
      }
    }
    
    return {
      found: result !== null,
      result: result,
      confidence: Math.min(95, confidence)
    };
  }

  analyzeColorsForGameResult(colorAnalysis) {
    if (!colorAnalysis || !colorAnalysis.dominantColors) {
      return { found: false, result: null, confidence: 0 };
    }
    
    let victoryScore = 0;
    let defeatScore = 0;
    
    // Enhanced color matching with distance calculation
    for (const dominantColor of colorAnalysis.dominantColors.slice(0, 5)) {
      const color = dominantColor.color;
      
      // Calculate distances to victory colors
      for (const victoryColor of this.victoryColors) {
        const distance = this.calculateColorDistance(color, victoryColor);
        if (distance < 80) {
          victoryScore += (100 - distance) * dominantColor.percentage / 100;
        }
      }
      
      // Calculate distances to defeat colors
      for (const defeatColor of this.defeatColors) {
        const distance = this.calculateColorDistance(color, defeatColor);
        if (distance < 80) {
          defeatScore += (100 - distance) * dominantColor.percentage / 100;
        }
      }
    }
    
    // Determine result based on scores
    if (victoryScore > defeatScore && victoryScore > 15) {
      return {
        found: true,
        result: 'victory',
        confidence: Math.min(70, Math.round(victoryScore * 2))
      };
    } else if (defeatScore > victoryScore && defeatScore > 15) {
      return {
        found: true,
        result: 'defeat',
        confidence: Math.min(70, Math.round(defeatScore * 2))
      };
    }
    
    return { found: false, result: null, confidence: 0 };
  }

  calculateColorDistance(color1, color2) {
    const dr = color1.r - color2.r;
    const dg = color1.g - color2.g;
    const db = color1.b - color2.b;
    return Math.sqrt(dr * dr + dg * dg + db * db);
  }

  async analyzeImageColors(imagePath) {
    try {
      // This is a placeholder for actual color analysis
      // In a real implementation, you would use an image processing library
      console.log(`🎨 Analyzing colors in ${imagePath}`);
      
      // Mock color analysis result for now
      return {
        dominantColors: [
          { color: { r: 255, g: 215, b: 0 }, percentage: 25 },  // Gold
          { color: { r: 0, g: 0, b: 0 }, percentage: 20 },      // Black
          { color: { r: 255, g: 255, b: 255 }, percentage: 15 } // White
        ],
        averageColor: { r: 128, g: 128, b: 128 }
      };
    } catch (error) {
      console.error('Error in color analysis:', error);
      throw error;
    }
  }
  
  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
  
  // Get analysis statistics
  getAnalysisStats() {
    return {
      totalAnalyzed: this.totalAnalyzed || 0,
      victoryDetected: this.victoryDetected || 0,
      defeatDetected: this.defeatDetected || 0,
      unknownResults: this.unknownResults || 0
    };
  }
  
  // Check if analysis tools are available
  async checkAvailableTools() {
    const tools = {
      tesseract: false,
      imagemagick: false
    };
    
    try {
      await execAsync('tesseract --version');
      tools.tesseract = true;
    } catch (e) {
      // Tesseract not available
    }
    
    try {
      await execAsync('magick -version');
      tools.imagemagick = true;
    } catch (e) {
      // ImageMagick not available
    }
    
    return tools;
  }
}

module.exports = ImageAnalyzer; 