<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Cache Management - Guard Tower</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .cache-stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .platform-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .platform-youtube {
            background-color: #ff0000;
            color: white;
        }
        
        .platform-twitch {
            background-color: #9146ff;
            color: white;
        }
        
        .error-indicator {
            color: #dc3545;
        }
        
        .success-indicator {
            color: #28a745;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
</head>
<body>
    <div id="navbar-container"></div>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1><i class="fas fa-images"></i> Image Cache Management</h1>
                <p class="text-muted">Monitor and manage profile image caching for YouTube and Twitch channels</p>
            </div>
        </div>
        
        <!-- Cache Statistics -->
        <div class="row">
            <div class="col-12">
                <div class="cache-stats-card">
                    <h3><i class="fas fa-chart-bar"></i> Cache Statistics</h3>
                    <div id="cache-stats-content">
                        <div class="text-center">
                            <i class="fas fa-spinner fa-spin fa-2x"></i>
                            <p>Loading cache statistics...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Management Actions -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-tools"></i> Cache Management</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="refreshCacheStats()">
                                <i class="fas fa-sync"></i> Refresh Statistics
                            </button>
                            <button class="btn btn-warning" onclick="clearPlatformCache('youtube')">
                                <i class="fab fa-youtube"></i> Clear YouTube Cache
                            </button>
                            <button class="btn btn-warning" onclick="clearPlatformCache('twitch')">
                                <i class="fab fa-twitch"></i> Clear Twitch Cache
                            </button>
                            <button class="btn btn-info" onclick="refreshAllImages()">
                                <i class="fas fa-images"></i> Refresh All Images
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exclamation-triangle"></i> Error Monitoring</h5>
                    </div>
                    <div class="card-body">
                        <div id="error-monitoring">
                            <p class="text-muted">Error information will be displayed here</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Activity Log -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Activity Log</h5>
                    </div>
                    <div class="card-body">
                        <div id="activity-log" style="max-height: 400px; overflow-y: auto;">
                            <!-- Activity entries will be added here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/main.js"></script>
    <script>
        let activityLog = [];
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🔄 Initializing admin-image-cache page...');

            // Load unified navigation
            if (typeof window.loadNavigation === 'function') {
                await window.loadNavigation();
            } else if (typeof window.loadNavbar === 'function') {
                await window.loadNavbar();
            } else {
                await loadComponent('navbar');
            }

            // Update navbar profile
            setTimeout(async () => {
                if (window.updateNavbarProfileUnified) {
                    console.log('🔄 Updating navbar profile (unified) on admin-image-cache page');
                    await window.updateNavbarProfileUnified();
                } else if (window.updateNavbarProfile) {
                    console.log('🔄 Updating navbar profile (legacy) on admin-image-cache page');
                    await window.updateNavbarProfile();
                }
            }, 500);

            await refreshCacheStats();

            // Auto-refresh every 30 seconds
            setInterval(refreshCacheStats, 30000);
        });
        
        // Add activity to log
        function addActivity(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? 'fa-exclamation-circle' : 
                        type === 'success' ? 'fa-check-circle' : 'fa-info-circle';
            const colorClass = type === 'error' ? 'text-danger' : 
                              type === 'success' ? 'text-success' : 'text-info';
            
            activityLog.unshift({
                timestamp,
                message,
                type,
                icon,
                colorClass
            });
            
            // Keep only last 50 entries
            if (activityLog.length > 50) {
                activityLog = activityLog.slice(0, 50);
            }
            
            updateActivityDisplay();
        }
        
        // Update activity display
        function updateActivityDisplay() {
            const logContainer = document.getElementById('activity-log');
            logContainer.innerHTML = activityLog.map(entry => `
                <div class="d-flex align-items-center mb-2">
                    <i class="fas ${entry.icon} ${entry.colorClass} me-2"></i>
                    <span class="text-muted me-2">${entry.timestamp}</span>
                    <span>${entry.message}</span>
                </div>
            `).join('');
        }
        
        // Refresh cache statistics
        async function refreshCacheStats() {
            try {
                addActivity('Refreshing cache statistics...');
                
                const response = await fetch('/api/channels/cache-stats', {
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                displayCacheStats(data);
                addActivity('Cache statistics refreshed successfully', 'success');
                
            } catch (error) {
                console.error('Error refreshing cache stats:', error);
                addActivity(`Error refreshing cache stats: ${error.message}`, 'error');
                
                document.getElementById('cache-stats-content').innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                        <p>Error loading cache statistics</p>
                        <small>${error.message}</small>
                    </div>
                `;
            }
        }
        
        // Display cache statistics
        function displayCacheStats(data) {
            const { cacheStats, userStats } = data;
            
            let content = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="stat-item">
                            <h6><i class="fas fa-users"></i> User Statistics</h6>
                            <p><strong>Total Users:</strong> ${userStats.total}</p>
                            <p><strong>With YouTube:</strong> ${userStats.withYoutube}</p>
                            <p><strong>With Twitch:</strong> ${userStats.withTwitch}</p>
                            <p><strong>With Errors:</strong> <span class="error-indicator">${userStats.withErrors}</span></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stat-item">
                            <h6><i class="fas fa-database"></i> Cache Statistics</h6>
            `;
            
            if (cacheStats && cacheStats.length > 0) {
                cacheStats.forEach(stat => {
                    const platformClass = stat._id === 'youtube' ? 'platform-youtube' : 'platform-twitch';
                    const avgAgeHours = Math.round(stat.avgAge / (1000 * 60 * 60));
                    
                    content += `
                        <div class="mb-2">
                            <span class="platform-badge ${platformClass}">${stat._id.toUpperCase()}</span>
                            <span>Cached: ${stat.count} | Errors: ${stat.errors} | Avg Age: ${avgAgeHours}h</span>
                        </div>
                    `;
                });
            } else {
                content += '<p class="text-muted">No cache data available</p>';
            }
            
            content += `
                        </div>
                    </div>
                </div>
            `;
            
            document.getElementById('cache-stats-content').innerHTML = content;
        }
        
        // Clear platform cache
        async function clearPlatformCache(platform) {
            if (!confirm(`Are you sure you want to clear all ${platform} cache entries?`)) {
                return;
            }
            
            try {
                addActivity(`Clearing ${platform} cache...`);
                
                const response = await fetch(`/api/channels/cache/${platform}`, {
                    method: 'DELETE',
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addActivity(`${data.message} (${data.clearedEntries} entries)`, 'success');
                
                // Refresh stats after clearing
                setTimeout(refreshCacheStats, 1000);
                
            } catch (error) {
                console.error('Error clearing cache:', error);
                addActivity(`Error clearing ${platform} cache: ${error.message}`, 'error');
            }
        }
        
        // Refresh all images
        async function refreshAllImages() {
            if (!confirm('This will refresh profile images for all content creators. Continue?')) {
                return;
            }
            
            try {
                addActivity('Starting bulk image refresh...');
                
                const response = await fetch('/api/channels/refresh-all-images', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        batchSize: 5,
                        force: false
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                addActivity(`Bulk refresh completed: ${data.summary.updated} updated, ${data.summary.errors} errors`, 'success');
                
                // Refresh stats after bulk update
                setTimeout(refreshCacheStats, 2000);
                
            } catch (error) {
                console.error('Error refreshing all images:', error);
                addActivity(`Error during bulk refresh: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
