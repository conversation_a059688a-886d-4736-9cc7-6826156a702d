<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WC Arena - Dashboard</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Cinzel:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="/css/warcraft-app-modern.css">
    <link rel="stylesheet" href="/css/dashboard-modern.css">
    
    <style>
        /* Dashboard Specific Styles */
        .dashboard-grid {
            display: grid;
            grid-template-columns: 320px 1fr;
            min-height: 100vh;
            gap: 0;
        }
        
        .dashboard-sidebar {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--glass-border);
            padding: var(--space-6);
            overflow-y: auto;
        }
        
        .dashboard-main {
            padding: var(--space-6);
            overflow-y: auto;
        }
        
        .user-profile-section {
            margin-bottom: var(--space-8);
        }
        
        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            border: 3px solid var(--primary-gold);
            box-shadow: var(--shadow-glow);
            object-fit: cover;
        }
        
        .user-info h2 {
            color: var(--primary-gold);
            font-family: var(--font-display);
            margin-bottom: var(--space-2);
        }
        
        .user-meta {
            color: var(--neutral-400);
            font-size: var(--text-sm);
            margin-bottom: var(--space-4);
        }
        
        .quick-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-3);
            margin-bottom: var(--space-6);
        }
        
        .quick-stat {
            background: rgba(255, 255, 255, 0.05);
            padding: var(--space-3);
            border-radius: var(--radius-lg);
            text-align: center;
        }
        
        .quick-stat-value {
            font-size: var(--text-xl);
            font-weight: 700;
            color: var(--primary-gold);
            display: block;
        }
        
        .quick-stat-label {
            font-size: var(--text-xs);
            color: var(--neutral-400);
            text-transform: uppercase;
        }
        
        .players-section h3 {
            color: var(--neutral-200);
            margin-bottom: var(--space-4);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .add-player-btn {
            background: none;
            border: 1px solid var(--glass-border);
            color: var(--neutral-400);
            padding: var(--space-2);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: all var(--transition-fast);
        }
        
        .add-player-btn:hover {
            border-color: var(--primary-gold);
            color: var(--primary-gold);
        }
        
        .player-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }
        
        .mini-player-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
        }
        
        .mini-player-card:hover {
            border-color: var(--primary-gold);
            background: rgba(212, 175, 55, 0.1);
        }
        
        .mini-player-card.active {
            border-color: var(--primary-gold);
            background: rgba(212, 175, 55, 0.15);
        }
        
        .mini-player-info {
            display: flex;
            align-items: center;
            gap: var(--space-3);
        }
        
        .mini-player-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-gold);
            object-fit: cover;
        }
        
        .mini-player-details h4 {
            color: var(--primary-gold);
            margin: 0;
            font-size: var(--text-base);
        }
        
        .mini-player-rank {
            font-size: var(--text-xs);
            color: var(--neutral-400);
        }
        
        .sidebar-nav {
            margin-top: var(--space-8);
        }
        
        .sidebar-nav h3 {
            color: var(--neutral-200);
            margin-bottom: var(--space-4);
            font-size: var(--text-base);
        }
        
        .sidebar-nav-list {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }
        
        .sidebar-nav-item {
            padding: var(--space-3) var(--space-4);
            border-radius: var(--radius-lg);
            transition: all var(--transition-fast);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: var(--space-3);
            color: var(--neutral-400);
            text-decoration: none;
        }
        
        .sidebar-nav-item:hover,
        .sidebar-nav-item.active {
            background: rgba(212, 175, 55, 0.1);
            color: var(--primary-gold);
        }
        
        .sidebar-nav-item i {
            width: 20px;
            text-align: center;
        }
        
        .main-content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-6);
            margin-bottom: var(--space-6);
        }
        
        .content-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-6);
        }
        
        .section-title {
            color: var(--primary-gold);
            font-family: var(--font-display);
            font-size: var(--text-xl);
            margin: 0;
        }
        
        .section-action {
            color: var(--neutral-400);
            text-decoration: none;
            font-size: var(--text-sm);
            transition: color var(--transition-fast);
        }
        
        .section-action:hover {
            color: var(--primary-gold);
        }
        
        .recent-matches {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }
        
        .match-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            border-left: 4px solid transparent;
            transition: all var(--transition-fast);
        }
        
        .match-item.win {
            border-left-color: #10B981;
        }
        
        .match-item.loss {
            border-left-color: var(--horde-red);
        }
        
        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-2);
        }
        
        .match-result {
            font-weight: 600;
        }
        
        .match-result.win {
            color: #10B981;
        }
        
        .match-result.loss {
            color: var(--horde-red);
        }
        
        .match-time {
            color: var(--neutral-400);
            font-size: var(--text-sm);
        }
        
        .match-details {
            color: var(--neutral-300);
            font-size: var(--text-sm);
        }
        
        .activity-feed {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }
        
        .activity-item {
            display: flex;
            gap: var(--space-3);
            padding: var(--space-3);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-gold);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--neutral-900);
            font-size: var(--text-sm);
            flex-shrink: 0;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            color: var(--neutral-200);
            font-weight: 600;
            margin-bottom: var(--space-1);
        }
        
        .activity-description {
            color: var(--neutral-400);
            font-size: var(--text-sm);
        }
        
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--space-4);
            margin-bottom: var(--space-6);
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            text-align: center;
        }
        
        .stat-card-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--primary-gold);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto var(--space-3);
            color: var(--neutral-900);
        }
        
        .stat-card-value {
            font-size: var(--text-2xl);
            font-weight: 700;
            color: var(--primary-gold);
            display: block;
            margin-bottom: var(--space-1);
        }
        
        .stat-card-label {
            color: var(--neutral-400);
            font-size: var(--text-sm);
        }
        
        .leaderboard-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-3);
        }
        
        .leaderboard-rank {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--primary-gold);
            color: var(--neutral-900);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: var(--text-sm);
        }
        
        .leaderboard-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid var(--primary-gold);
            object-fit: cover;
        }
        
        .leaderboard-info {
            flex: 1;
        }
        
        .leaderboard-name {
            color: var(--primary-gold);
            font-weight: 600;
            margin-bottom: var(--space-1);
        }
        
        .leaderboard-mmr {
            color: var(--neutral-400);
            font-size: var(--text-sm);
        }
        
        .wide-section {
            grid-column: 1 / -1;
        }
        
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .dashboard-sidebar {
                order: 2;
                border-right: none;
                border-top: 1px solid var(--glass-border);
            }
            
            .main-content-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .stats-overview {
                grid-template-columns: 1fr;
            }
            
            .quick-stats {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
</head>
<body class="app-container">
    <!-- Header -->
    <header class="app-header">
        <nav class="nav-modern">
            <a href="/" class="nav-brand">
                <i class="fas fa-crown"></i>
                                    <span>WC Arena</span>
            </a>
            
            <ul class="nav-links">
                <li><a href="/dashboard-modern.html" class="nav-link active">Dashboard</a></li>
                <li><a href="/ladder.html" class="nav-link">Ladder</a></li>
                <li><a href="/tournaments.html" class="nav-link">Tournaments</a></li>
                <li><a href="/maps.html" class="nav-link">Maps</a></li>
                <li><a href="/forum.html" class="nav-link">Forum</a></li>
                <li><a href="/live.html" class="nav-link">Live</a></li>
                
                <li class="nav-user">
                    <div class="flex items-center gap-3">
                        <img src="/assets/img/default-avatar.svg" alt="User" class="w-8 h-8 rounded-full border-2 border-primary-gold">
                        <span id="nav-username" class="text-primary">Player</span>
                        <button class="btn btn-secondary btn-sm" onclick="logout()">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </li>
            </ul>
        </nav>
    </header>

    <!-- Dashboard Layout -->
    <div class="dashboard-grid">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar">
            <!-- User Profile Section -->
            <div class="user-profile-section">
                <div class="flex items-center gap-4 mb-4">
                    <img src="/assets/img/default-avatar.svg" alt="User Avatar" class="user-avatar" id="user-avatar">
                    <div class="user-info">
                        <h2 id="user-display-name">Loading...</h2>
                        <div class="user-meta">
                            <span id="user-join-date">Member since 2024</span>
                        </div>
                    </div>
                </div>
                
                <div class="quick-stats">
                    <div class="quick-stat">
                        <span class="quick-stat-value" id="total-players">0</span>
                        <span class="quick-stat-label">Players</span>
                    </div>
                    <div class="quick-stat">
                        <span class="quick-stat-value" id="total-games">0</span>
                        <span class="quick-stat-label">Games</span>
                    </div>
                    <div class="quick-stat">
                        <span class="quick-stat-value" id="highest-rank">Unranked</span>
                        <span class="quick-stat-label">Best Rank</span>
                    </div>
                    <div class="quick-stat">
                        <span class="quick-stat-value" id="arena-gold">0</span>
                        <span class="quick-stat-label">Arena Gold</span>
                    </div>
                </div>
            </div>

            <!-- Players Section -->
            <div class="players-section">
                <h3>
                    Your Players 
                    <button class="add-player-btn" title="Create New Player" onclick="createPlayer()">
                        <i class="fas fa-plus"></i>
                    </button>
                </h3>
                <div class="player-list" id="player-list">
                    <!-- Players will be loaded here -->
                </div>
            </div>

            <!-- Navigation -->
            <nav class="sidebar-nav">
                <h3>Quick Actions</h3>
                <ul class="sidebar-nav-list">
                    <li><a href="/myprofile" class="sidebar-nav-item">
                        <i class="fas fa-user"></i> My Profile
                    </a></li>
                    <li><a href="/ladder.html" class="sidebar-nav-item">
                        <i class="fas fa-trophy"></i> View Ladder
                    </a></li>
                    <li><a href="/tournaments.html" class="sidebar-nav-item">
                        <i class="fas fa-medal"></i> Tournaments
                    </a></li>
                    <li><a href="/maps.html" class="sidebar-nav-item">
                        <i class="fas fa-map"></i> Browse Maps
                    </a></li>
                    <li><a href="/forum.html" class="sidebar-nav-item">
                        <i class="fas fa-comments"></i> Community
                    </a></li>
                                    <li><a href="/campaigns.html" class="sidebar-nav-item">
                  <i class="fas fa-flag"></i> War Table
                    </a></li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Welcome Section -->
            <div class="mb-6">
                <h1 class="text-4xl text-display text-primary mb-2">Welcome back, <span id="welcome-username">Player</span>!</h1>
                <p class="text-muted">Ready to dominate the battlefield? Check your recent activity and jump into action.</p>
            </div>

            <!-- Stats Overview -->
            <div class="stats-overview">
                <div class="stat-card">
                    <div class="stat-card-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <span class="stat-card-value" id="win-rate">0%</span>
                    <span class="stat-card-label">Win Rate</span>
                </div>
                <div class="stat-card">
                    <div class="stat-card-icon">
                        <i class="fas fa-fire"></i>
                    </div>
                    <span class="stat-card-value" id="win-streak">0</span>
                    <span class="stat-card-label">Win Streak</span>
                </div>
                <div class="stat-card">
                    <div class="stat-card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <span class="stat-card-value" id="mmr-display">1200</span>
                    <span class="stat-card-label">Current MMR</span>
                </div>
            </div>

            <!-- Main Content Grid -->
            <div class="main-content-grid">
                <!-- Recent Matches -->
                <section class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">Recent Matches</h2>
                        <a href="/ladder.html" class="section-action">View All</a>
                    </div>
                    
                    <div class="recent-matches" id="recent-matches">
                        <!-- Matches will be loaded here -->
                    </div>
                </section>

                <!-- Recent Activity -->
                <section class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">Recent Activity</h2>
                        <a href="#" class="section-action">View All</a>
                    </div>
                    
                    <div class="activity-feed" id="activity-feed">
                        <!-- Activity will be loaded here -->
                    </div>
                </section>

                <!-- Current Leaderboard -->
                <section class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">Top Players</h2>
                        <a href="/ladder.html" class="section-action">Full Ladder</a>
                    </div>
                    
                    <div id="leaderboard-preview">
                        <!-- Leaderboard will be loaded here -->
                    </div>
                </section>

                <!-- Quick Actions -->
                <section class="content-section">
                    <div class="section-header">
                        <h2 class="section-title">Quick Actions</h2>
                    </div>
                    
                    <div class="flex flex-col gap-4">
                        <button class="btn btn-primary" onclick="findMatch()">
                            <i class="fas fa-sword-crossed"></i>
                            Find Match
                        </button>
                        <button class="btn btn-secondary" onclick="openPlayerCreate()">
                            <i class="fas fa-user-plus"></i>
                            Create New Player
                        </button>
                        <button class="btn btn-alliance" onclick="joinTournament()">
                            <i class="fas fa-trophy"></i>
                            Join Tournament
                        </button>
                        <button class="btn btn-horde" onclick="viewMaps()">
                            <i class="fas fa-map"></i>
                            Browse Maps
                        </button>
                    </div>
                </section>
            </div>

            <!-- Wide Sections -->
            <!-- Player Statistics Charts -->
            <section class="content-section wide-section">
                <div class="section-header">
                    <h2 class="section-title">Player Statistics</h2>
                    <div class="flex gap-2">
                        <button class="chart-toggle active" data-period="week">Week</button>
                        <button class="chart-toggle" data-period="month">Month</button>
                        <button class="chart-toggle" data-period="all">All Time</button>
                    </div>
                </div>
                
                <!-- Charts Grid -->
                <div class="charts-grid charts-grid-2">
                    <!-- Win/Loss Chart -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">Win/Loss Ratio</h3>
                            <div class="chart-subtitle">Current player performance</div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="dashboard-winloss-chart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-value" id="total-games">68</span>
                                <span class="chart-stat-label">Total Games</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-value" id="win-percentage">66%</span>
                                <span class="chart-stat-label">Win Rate</span>
                            </div>
                        </div>
                    </div>

                    <!-- Race Distribution Chart -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">Race Distribution</h3>
                            <div class="chart-subtitle">Preferred races across all games</div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="dashboard-race-chart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-value">44%</span>
                                <span class="chart-stat-label">Human</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-value">37%</span>
                                <span class="chart-stat-label">Orc</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-value">19%</span>
                                <span class="chart-stat-label">Other</span>
                            </div>
                        </div>
                    </div>

                    <!-- MMR Progression Chart -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">MMR Progress</h3>
                            <div class="chart-subtitle">Recent ranking progression</div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="dashboard-mmr-chart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-value text-alliance">+50</span>
                                <span class="chart-stat-label">This Week</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-value">1250</span>
                                <span class="chart-stat-label">Current MMR</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-value">1285</span>
                                <span class="chart-stat-label">Peak MMR</span>
                            </div>
                        </div>
                    </div>

                    <!-- Match Types Chart -->
                    <div class="chart-container">
                        <div class="chart-header">
                            <h3 class="chart-title">Game Modes</h3>
                            <div class="chart-subtitle">Distribution by match type</div>
                        </div>
                        <div class="chart-wrapper">
                            <canvas id="dashboard-matchtypes-chart"></canvas>
                        </div>
                        <div class="chart-stats">
                            <div class="chart-stat-item">
                                <span class="chart-stat-value">45</span>
                                <span class="chart-stat-label">1v1 Games</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-value">18</span>
                                <span class="chart-stat-label">2v2 Games</span>
                            </div>
                            <div class="chart-stat-item">
                                <span class="chart-stat-value">5</span>
                                <span class="chart-stat-label">Team Games</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section class="content-section wide-section">
                <div class="section-header">
                    <h2 class="section-title">Live Streams & Events</h2>
                    <a href="/live.html" class="section-action">View Live Page</a>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4" id="live-content">
                    <!-- Live content will be loaded here -->
                </div>
            </section>
        </main>
    </div>

    <!-- Player Creation Modal -->
    <div class="modal-overlay" id="player-create-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Create New Player</h3>
                <button class="modal-close" onclick="closePlayerCreate()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="player-create-form">
                    <div class="form-group">
                        <label class="form-label">Player Name</label>
                        <input type="text" class="form-input" name="playerName" placeholder="Enter player name" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Faction</label>
                        <div class="flex gap-4">
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="radio" name="faction" value="alliance" required>
                                <span class="text-alliance">Alliance</span>
                            </label>
                            <label class="flex items-center gap-2 cursor-pointer">
                                <input type="radio" name="faction" value="horde" required>
                                <span class="text-horde">Horde</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Race</label>
                        <select class="form-input" name="race" required>
                            <option value="">Select Race</option>
                            <option value="human">Human</option>
                            <option value="orc">Orc</option>
                            <option value="undead">Undead</option>
                            <option value="night-elf">Night Elf</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closePlayerCreate()">Cancel</button>
                <button type="submit" form="player-create-form" class="btn btn-primary">Create Player</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div id="footer-container"></div>

    <!-- Scripts -->
    <!-- Load main.js first to handle navbar loading and chat system -->
    <script src="/js/main.js"></script>
    <!-- Add Chart.js for statistics -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.1/dist/chart.umd.min.js"></script>
    <script src="/js/chart-utils.js"></script>
    <script src="/js/warcraft-app-modern.js"></script>
    <script>
        // Dashboard initialization will be handled by the main JS file
        
        // Load footer component
        async function loadFooter() {
            try {
                const response = await fetch('/components/footer.html');
                if (response.ok) {
                    const footerHTML = await response.text();
                    const footerContainer = document.getElementById('footer-container');
                    if (footerContainer) {
                        footerContainer.innerHTML = footerHTML;
                    }
                }
            } catch (error) {
                console.error('Failed to load footer:', error);
            }
        }
        
        // Load footer when page loads
        document.addEventListener('DOMContentLoaded', loadFooter);
        
        // Update navbar profile with user data after page load
        window.addEventListener('load', async () => {
            setTimeout(async () => {
                if (window.updateNavbarProfile && typeof window.updateNavbarProfile === 'function') {
                    console.log('🔄 Updating navbar profile on dashboard page');
                    await window.updateNavbarProfile();
                }
            }, 500);
        });
    </script>
</body>
</html> 
