/* ==========================================================================
   PLAYER MODAL STYLING FIXES
   Focused on fixing spacing, visibility, and layout issues
   ========================================================================== */

/* ===== MODAL OVERLAY ===== */
#player-stats-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

#player-stats-modal.show {
  opacity: 1;
  visibility: visible;
}

/* ===== MODAL CONTENT ===== */
#player-stats-modal .modal-content {
  background: #1a1a1a;
  border-radius: 12px;
  border: 2px solid #D4AF37;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
  max-width: 90vw;
  max-height: 90vh;
  width: 1200px;
  overflow: hidden;
  position: relative;
}

/* ===== PLAYER MODAL CONTAINER ===== */
.player-modal-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
}

/* ===== PLAYER HEADER - FIXED SPACING ===== */
.player-modal-header {
  background: linear-gradient(135deg, #2a2a2a, #1a1a1a);
  border-bottom: 2px solid #D4AF37;
  padding: 20px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  min-height: 80px;
}

.player-info {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.player-rank {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: #333;
  border: 2px solid #D4AF37;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.player-rank .rank-icon {
  width: 50px;
  height: 50px;
  object-fit: contain;
  border-radius: 4px;
}

.player-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
}

.player-name {
  font-size: 1.8rem;
  font-weight: 700;
  color: #D4AF37;
  margin: 0;
  line-height: 1.2;
}

.player-race-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.race-display {
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
  padding: 4px 10px;
  background: #333;
  border-radius: 15px;
  border: 1px solid #555;
}

.player-rank-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.rank-name {
  font-size: 1rem;
  font-weight: 600;
  color: #D4AF37;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.player-mmr {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 20px;
  background: #333;
  border-radius: 8px;
  border: 1px solid #D4AF37;
  min-width: 80px;
  flex-shrink: 0;
}

.mmr-value {
  font-size: 1.3rem;
  font-weight: 700;
  color: #D4AF37;
}

.mmr-label {
  font-size: 0.7rem;
  color: #ccc;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== CLOSE BUTTON - FIXED POSITIONING ===== */
.close-modal {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: #333;
  border: 1px solid #D4AF37;
  color: #D4AF37;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  z-index: 10;
}

.close-modal:hover {
  background: #D4AF37;
  color: #1a1a1a;
  transform: scale(1.1);
}

/* ===== MODAL TABS ===== */
.modal-tabs {
  display: flex;
  background: #2a2a2a;
  border-bottom: 1px solid #D4AF37;
}

.modal-tab {
  padding: 15px 25px;
  background: transparent;
  border: none;
  color: #ccc;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 3px solid transparent;
}

.modal-tab:hover {
  color: #D4AF37;
  background: #333;
}

.modal-tab.active {
  color: #D4AF37;
  border-bottom-color: #D4AF37;
  background: #333;
}

/* ===== TAB CONTENT ===== */
.modal-tab-content {
  display: none;
  flex: 1;
  overflow: hidden;
  padding: 0;
}

.modal-tab-content.active {
  display: flex;
  flex-direction: column;
}

/* ===== OVERVIEW CONTENT ===== */
.overview-content {
  padding: 30px;
  flex: 1;
  overflow-y: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #2a2a2a;
  border-radius: 10px;
  border: 1px solid #D4AF37;
  padding: 20px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(212, 175, 55, 0.2);
}

.stat-content {
  text-align: center;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: #D4AF37;
  margin-bottom: 8px;
}

.stat-value.positive {
  color: #4ade80;
}

.stat-value.negative {
  color: #f87171;
}

.stat-label {
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== MATCHES CONTENT ===== */
.matches-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.matches-list {
  flex: 1;
  overflow-y: auto;
  padding: 20px 30px;
}

/* ===== MATCH ITEM - FIXED VISIBILITY ===== */
.match-item {
  background: #2a2a2a;
  border-radius: 10px;
  border: 1px solid #444;
  margin-bottom: 15px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
}

.match-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border-color: #D4AF37;
}

.match-item.match-win {
  border-left: 4px solid #4ade80;
}

.match-item.match-loss {
  border-left: 4px solid #f87171;
}

.match-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: #333;
  border-bottom: 1px solid #444;
}

.match-outcome {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 0.9rem;
}

.match-win .match-outcome {
  color: #4ade80;
}

.match-loss .match-outcome {
  color: #f87171;
}

.outcome-text {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.match-type {
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
}

.match-date {
  font-size: 0.8rem;
  color: #999;
  font-weight: 500;
}

.match-expand-icon {
  color: #ccc;
  transition: all 0.3s ease;
}

.match-item:hover .match-expand-icon {
  color: #D4AF37;
}

.match-details {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 15px;
}

.match-map {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #ccc;
  font-weight: 500;
}

.match-players-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.match-players {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.9rem;
}

.player-link {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.player-link:hover {
  background: #333;
  transform: scale(1.05);
}

.player-link.winner {
  color: #4ade80;
}

.player-link.loser {
  color: #f87171;
}

.vs-separator {
  color: #999;
  font-weight: 600;
  font-size: 0.8rem;
}

.mmr-change {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  padding: 6px 12px;
  border-radius: 8px;
  min-width: 100px;
  justify-content: center;
}

.mmr-change.positive {
  color: #4ade80;
  background: rgba(74, 222, 128, 0.1);
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.mmr-change.negative {
  color: #f87171;
  background: rgba(248, 113, 113, 0.1);
  border: 1px solid rgba(248, 113, 113, 0.3);
}

/* ===== EXPANDED MATCH DETAILS - FIXED VISIBILITY ===== */
.match-details-expanded {
  background: #333;
  border-top: 1px solid #444;
  padding: 20px;
}

.match-details-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.match-details-section {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 15px;
  border: 1px solid #444;
}

.match-details-section h4 {
  color: #D4AF37;
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.match-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.team-detail {
  background: #333;
  border-radius: 6px;
  padding: 12px;
  border: 1px solid #444;
}

.team-detail.winning-team {
  border-color: rgba(74, 222, 128, 0.5);
  background: rgba(74, 222, 128, 0.05);
}

.team-detail.losing-team {
  border-color: rgba(248, 113, 113, 0.5);
  background: rgba(248, 113, 113, 0.05);
}

.team-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
  font-weight: 600;
}

.team-label {
  color: #ccc;
}

.team-result.winner {
  color: #4ade80;
}

.team-result.loser {
  color: #f87171;
}

.team-players-detailed {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.player-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2a2a2a;
  border-radius: 4px;
  font-size: 0.9rem;
}

.player-detail.current-player {
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.3);
}

.player-detail-header {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.player-detail-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ccc;
  font-size: 0.8rem;
}

.result-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.result-badge.winner {
  background: rgba(74, 222, 128, 0.2);
  color: #4ade80;
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.result-badge.loser {
  background: rgba(248, 113, 113, 0.2);
  color: #f87171;
  border: 1px solid rgba(248, 113, 113, 0.3);
}

.match-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.match-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #333;
  border-radius: 4px;
  font-size: 0.9rem;
}

.info-label {
  color: #ccc;
  font-weight: 500;
}

.info-value {
  color: #fff;
  font-weight: 600;
}

.info-value.positive {
  color: #4ade80;
}

.info-value.negative {
  color: #f87171;
}

/* ===== PAGINATION ===== */
.matches-pagination {
  background: #2a2a2a;
  border-top: 1px solid #D4AF37;
  padding: 20px 30px;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 0.9rem;
  color: #ccc;
}

.pagination-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  border: 1px solid #D4AF37;
  background: #333;
  color: #D4AF37;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn:hover:not(:disabled) {
  background: #D4AF37;
  color: #1a1a1a;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #D4AF37;
  color: #1a1a1a;
}

.btn-secondary {
  background: #333;
  border-color: #666;
  color: #ccc;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-ellipsis {
  color: #999;
  padding: 0 8px;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  #player-stats-modal .modal-content {
    width: 95vw;
    max-height: 95vh;
  }
  
  .player-modal-header {
    padding: 15px 20px;
  }
  
  .player-rank {
    width: 50px;
    height: 50px;
  }
  
  .player-rank .rank-icon {
    width: 40px;
    height: 40px;
  }
  
  .player-name {
    font-size: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
  }
  
  .stat-value {
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .player-modal-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 15px;
  }
  
  .player-info {
    flex-direction: column;
    gap: 15px;
  }
  
  .modal-tabs {
    padding: 0 10px;
  }
  
  .modal-tab {
    padding: 12px 15px;
    font-size: 0.9rem;
  }
  
  .overview-content,
  .matches-list {
    padding: 15px;
  }
  
  .match-header {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
  
  .match-details {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }
  
  .match-players {
    flex-direction: column;
    gap: 8px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .match-info-grid {
    grid-template-columns: 1fr;
  }
}

/* ===== SCROLLBAR STYLING ===== */
.matches-list::-webkit-scrollbar {
  width: 8px;
}

.matches-list::-webkit-scrollbar-track {
  background: #2a2a2a;
  border-radius: 4px;
}

.matches-list::-webkit-scrollbar-thumb {
  background: #D4AF37;
  border-radius: 4px;
}

.matches-list::-webkit-scrollbar-thumb:hover {
  background: #b8941f;
}

/* ===== LOADING STATE ===== */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #ccc;
  font-style: italic;
} 