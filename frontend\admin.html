<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Control Panel - WC Arena</title>
    
    <!-- CSS Files -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
    <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
    <link rel="stylesheet" href="/css/admin.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Admin-specific navbar styling */
        .admin-body .navbar {
            background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
            border-bottom: 2px solid var(--warcraft-gold);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        }
        
        .admin-body .navbar .nav-logo span::after {
            content: ' - Admin Panel';
            color: var(--warcraft-gold);
            font-size: 0.8em;
            font-weight: normal;
        }
        
        .admin-layout {
            margin-top: 80px; /* Account for navbar height */
        }
        
        .admin-header {
            display: none; /* Hide the old admin header since we're using the main navbar */
        }
    </style>
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
</head>
<body class="admin-body">
    <!-- Main Site Navbar -->
    <div id="navbar-container"></div>
    
    <!-- Admin Header (hidden, keeping for potential future use) -->
    <header class="admin-header" style="display: none;">
        <div class="admin-header-content">
            <div class="admin-logo">
                <i class="fas fa-shield-alt"></i>
                <span>Admin Control Panel</span>
            </div>
            
            <div class="admin-header-actions">
                <div class="admin-user-info">
                    <img src="/assets/img/default-avatar.svg" alt="Admin" class="admin-avatar">
                    <span class="admin-username">Administrator</span>
                    <div class="admin-dropdown">
                        <button class="admin-dropdown-btn">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="admin-dropdown-menu">
                            <a href="/myprofile.html" class="dropdown-item">
                                <i class="fas fa-user"></i> My Profile
                            </a>
                            <a href="/index.html" class="dropdown-item">
                                <i class="fas fa-university"></i> Main Site
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item" onclick="window.handleGlobalLogout(event);">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
                
                <button class="admin-notifications-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Admin Layout -->
    <div class="admin-layout">
        <!-- Sidebar Navigation -->
        <aside class="admin-sidebar">
            <nav class="admin-nav">
                <div class="nav-section">
                    <h3>Overview</h3>
                    <a href="#" class="admin-nav-item active" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>Analytics</span>
                    </a>
                </div>

                <div class="nav-section">
                    <h3>User Management</h3>
                    <a href="#" class="admin-nav-item" data-section="users">
                        <i class="fas fa-users"></i>
                        <span>Users</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="moderation">
                        <i class="fas fa-gavel"></i>
                        <span>Moderation</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="feedback">
                        <i class="fas fa-comment-dots"></i>
                        <span>Feedback</span>
                    </a>
                </div>

                <div class="nav-section">
                    <h3>Game Management</h3>
                    <a href="#" class="admin-nav-item" data-section="matches">
                        <i class="fas fa-cogs"></i>
                        <span>Matches</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="tournaments">
                        <i class="fas fa-trophy"></i>
                        <span>Tournaments</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="clans">
                        <i class="fas fa-shield-alt"></i>
                        <span>Clans</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="maps">
                        <i class="fas fa-map"></i>
                        <span>Maps</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="achievements">
                        <i class="fas fa-medal"></i>
                        <span>Achievements</span>
                    </a>
                </div>

                <div class="nav-section">
                    <h3>Support</h3>
                    <a href="#" class="admin-nav-item" data-section="disputes">
                        <i class="fas fa-flag"></i>
                        <span>Disputes</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="content">
                        <i class="fas fa-file-alt"></i>
                        <span>Content</span>
                    </a>
                </div>

                <div class="nav-section">
                    <h3>System</h3>
                    <a href="#" class="admin-nav-item" data-section="system">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                    <a href="#" class="admin-nav-item" data-section="image-cache">
                        <i class="fas fa-images"></i>
                        <span>Image Cache</span>
                    </a>
                </div>
            </nav>

            <!-- Quick Stats -->
            <div class="sidebar-stats">
                <h4>Quick Stats</h4>
                <div class="stat-item">
                    <span class="stat-label">Online Users</span>
                    <span class="stat-value">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Active Matches</span>
                    <span class="stat-value">-</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Server Load</span>
                    <span class="stat-value">-</span>
                </div>
            </div>
        </aside>

        <!-- Main Content Area -->
        <main class="admin-main">
            <div id="admin-content" class="admin-content">
                <!-- Content will be loaded dynamically -->
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <p>Loading admin panel...</p>
                </div>
            </div>
        </main>
    </div>

    <!-- Notification System -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Modal Container -->
    <div id="modal-container" class="modal-container"></div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner large"></div>
            <p id="loading-message">Processing...</p>
        </div>
    </div>

    <!-- JavaScript Files -->
    <!-- Load main.js first to handle navbar loading -->
    <script src="/js/utils.js"></script>
    <script src="/js/main.js"></script>
    <script type="module">
        import { AdminControlPanel } from '/js/modules/AdminControlPanel.js';
        import { ApiClient } from '/js/modules/ApiClient.js';
        import { UIManager } from '/js/modules/UIManager.js';
        import { GameManager } from '/js/modules/GameManager.js';
        import { ClanManager } from '/js/modules/ClanManager.js';
        import { TournamentManager } from '/js/modules/TournamentManager.js';
        import { AchievementEngine } from '/js/core/AchievementEngine.js';

        // Initialize admin control panel when page loads
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                console.log('Initializing admin control panel...');
                
                // Create global instances
                window.ApiClient = ApiClient; // Expose the constructor
                window.apiClient = new ApiClient(); // Create an instance
                window.uiManager = new UIManager();
                window.gameManager = new GameManager();
                window.clanManager = new ClanManager();
                window.tournamentManager = new TournamentManager(window.apiClient);
                window.achievementEngine = new AchievementEngine();
                window.adminControlPanel = new AdminControlPanel();
                
                // Initialize the admin panel
                await window.adminControlPanel.init();
                
                console.log('Admin control panel initialized successfully');
            } catch (error) {
                console.error('Failed to initialize admin control panel:', error);
                
                // Show error message
                const contentContainer = document.getElementById('admin-content');
                if (contentContainer) {
                    contentContainer.innerHTML = `
                        <div class="error-state">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h2>Access Denied</h2>
                            <p>You don't have permission to access the admin panel.</p>
                            <a href="/index.html" class="btn btn-primary">
                                <i class="fas fa-university"></i> Return to Main Site
                            </a>
                        </div>
                    `;
                }
            }
        });
    </script>

    <!-- Additional CSS for admin-specific styling -->
    <style>
        .admin-body {
            margin: 0;
            padding: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
        }

        .admin-header {
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 60px;
        }

        .admin-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
            padding: 0 var(--spacing-lg);
        }

        .admin-logo {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .admin-header-actions {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .admin-user-info {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            position: relative;
        }

        .admin-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .admin-dropdown {
            position: relative;
        }

        .admin-dropdown-btn {
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--spacing-xs);
        }

        .admin-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            min-width: 200px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
        }

        .admin-dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-primary);
            text-decoration: none;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: var(--bg-secondary);
        }

        .dropdown-divider {
            height: 1px;
            background-color: var(--border-color);
            margin: var(--spacing-xs) 0;
        }

        .admin-notifications-btn {
            position: relative;
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--spacing-sm);
            border-radius: var(--border-radius);
            transition: background-color 0.2s ease;
        }

        .admin-notifications-btn:hover {
            background-color: var(--bg-secondary);
        }

        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--danger-color);
            color: white;
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        .admin-layout {
            display: flex;
            margin-top: 60px;
            min-height: calc(100vh - 60px);
        }

        .admin-sidebar {
            width: 280px;
            background: var(--bg-primary);
            border-right: 1px solid var(--border-color);
            padding: var(--spacing-lg);
            overflow-y: auto;
        }

        .admin-nav {
            margin-bottom: var(--spacing-xl);
        }

        .nav-section {
            margin-bottom: var(--spacing-lg);
        }

        .nav-section h3 {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: var(--spacing-sm);
        }

        .admin-nav-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-sm) var(--spacing-md);
            color: var(--text-primary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: all 0.2s ease;
            margin-bottom: var(--spacing-xs);
        }

        .admin-nav-item:hover {
            background-color: var(--bg-secondary);
            color: var(--primary-color);
        }

        .admin-nav-item.active {
            background-color: var(--primary-color);
            color: white;
        }

        .nav-badge {
            margin-left: auto;
            background: var(--bg-secondary);
            color: var(--text-secondary);
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        .nav-badge.warning {
            background: var(--warning-color);
            color: white;
        }

        .nav-badge.danger {
            background: var(--danger-color);
            color: white;
        }

        .nav-badge.info {
            background: var(--info-color);
            color: white;
        }

        .sidebar-stats {
            border-top: 1px solid var(--border-color);
            padding-top: var(--spacing-md);
        }

        .sidebar-stats h4 {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-xs) 0;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .stat-value {
            font-weight: 600;
            color: var(--primary-color);
        }

        .admin-main {
            flex: 1;
            padding: var(--spacing-lg);
            overflow-y: auto;
        }

        .admin-content {
            max-width: 100%;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 400px;
            gap: var(--spacing-md);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid var(--border-color);
            border-top: 3px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .loading-spinner.large {
            width: 60px;
            height: 60px;
            border-width: 4px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-state {
            text-align: center;
            padding: var(--spacing-xl);
        }

        .error-state i {
            font-size: 4rem;
            color: var(--danger-color);
            margin-bottom: var(--spacing-md);
        }

        .error-state h2 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .error-state p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .admin-sidebar {
                position: fixed;
                left: -280px;
                top: 60px;
                height: calc(100vh - 60px);
                z-index: 999;
                transition: left 0.3s ease;
            }

            .admin-sidebar.mobile-open {
                left: 0;
            }

            .admin-main {
                width: 100%;
            }

            .admin-header-content {
                padding: 0 var(--spacing-md);
            }
        }
    </style>

    <!-- Essential Scripts for Navbar and User Management -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="/js/api-config.js"></script>
    <script src="/js/navbar-modern.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/modules/AdminControlPanel.js"></script>
    <script src="/js/utils.js"></script>
    <script src="/js/navbar-debug.js"></script>

    <script>
        // Initialize admin panel when page loads
        window.addEventListener('load', async () => {
            console.log('🔄 Admin page loaded, initializing...');

            // Load unified navigation first
            if (typeof window.loadNavigation === 'function') {
                await window.loadNavigation();
            } else if (typeof window.loadNavbar === 'function') {
                await window.loadNavbar();
            }

            // Initialize admin control panel
            if (window.AdminControlPanel) {
                window.adminPanel = new window.AdminControlPanel();
                await window.adminPanel.init();
            }

            // Update navbar profile with user data
            setTimeout(async () => {
                if (window.updateNavbarProfileUnified) {
                    console.log('🔄 Updating navbar profile (unified) on admin page');
                    await window.updateNavbarProfileUnified();
                } else if (window.updateNavbarProfile) {
                    console.log('🔄 Updating navbar profile (legacy) on admin page');
                    await window.updateNavbarProfile();
                }
            }, 500);
        });
    </script>
</body>
</html>
