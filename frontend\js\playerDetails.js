/**
 * PlayerDetails.js - Player Statistics Modal for Profile Page
 * 
 * This file handles the player statistics modal specifically for the profile page.
 * Updated to use the new ModalManager system.
 */

// Ensure this only runs on profile pages to avoid conflicts with ladder page
if (window.location.pathname.includes('/views/myprofile.html') || 
    window.location.pathname.includes('/profile') ||
    window.location.pathname.includes('/ladder') ||
    document.getElementById('player-stats-modal')) {
  
  console.log('🎯 PlayerDetails.js loaded for profile/ladder page');

/**
 * Player Details Module
 * Handles displaying detailed player statistics in a modal using the new ModalManager
 */

// Global utility functions using the new modal system
window.showLoadingModal = function(message) {
  if (window.modalManager) {
    const modalId = window.modalManager.createModal({
      id: 'loading-modal',
      title: 'Loading',
      content: `<div class="loading-container"><div class="loading-spinner"></div><p>${message}</p></div>`,
      size: 'sm',
      showCloseButton: false,
      backdrop: false,
      keyboard: false
    });
    window.modalManager.show('loading-modal');
  }
};

window.hideLoadingModal = function() {
  if (window.modalManager) {
    window.modalManager.hide('loading-modal');
  }
};

window.showErrorModal = function(message) {
  if (window.modalManager) {
    window.modalManager.alert(message, 'Error');
  }
};

window.viewScreenshots = function(screenshots) {
  // Create modal container if it doesn't exist
  let modal = document.getElementById('screenshot-modal');

  if (!modal) {
      modal = document.createElement('div');
      modal.id = 'screenshot-modal';
      modal.className = 'modal';
      document.body.appendChild(modal);
  }

  // Create modal content
  let modalContent = `
      <div class="modal-content">
          <span class="close-modal">&times;</span>
          <div class="screenshot-container">
  `;

  // Add screenshots
  if (screenshots && screenshots.length > 0) {
      screenshots.forEach((screenshot, index) => {
          modalContent += `
              <div class="screenshot-item" id="screenshot-${index}">
                  <img src="${screenshot.url}" alt="Match Screenshot">
                  <div class="screenshot-actions">
                      <button class="btn btn-danger flag-screenshot" data-screenshot-url="${screenshot.url}" data-screenshot-id="${screenshot._id || ''}">
                          <i class="fas fa-flag"></i> Flag Inappropriate Content
                      </button>
                  </div>
              </div>
          `;
      });
  } else {
      modalContent += '<div class="no-data">No screenshots available for this match.</div>';
  }


  modalContent += `
          </div>
      </div>
  `;

  // Set modal content
  modal.innerHTML = modalContent;

  // Show modal
  ModalManager.show('screenshot-modal');

  // Add event listener to close button
  const closeBtn = modal.querySelector('.close-modal');
  closeBtn.addEventListener('click', () => {
      ModalManager.hide('screenshot-modal');
      modal.remove(); // Remove modal from DOM when closed
  });

  // Add flag button event listeners
  const flagButtons = modal.querySelectorAll('.flag-screenshot');
  flagButtons.forEach(button => {
      button.addEventListener('click', () => {
          const screenshotUrl = button.dataset.screenshotUrl;
          const screenshotId = button.dataset.screenshotId;
          flagScreenshot(screenshotUrl, screenshotId);
      });
  });

  // Close modal when clicking outside of it
  window.addEventListener('click', (event) => {
      if (event.target === modal) {
          ModalManager.hide('screenshot-modal');
          modal.remove(); // Remove modal from DOM when closed
      }
  });
};

window.disputeMatch = async function(matchId) {
  try {
      // Remove any existing dispute modal first
      const existingDisputeModal = document.getElementById('dispute-modal');
      if (existingDisputeModal) {
          existingDisputeModal.remove();
      }

      // Create modal for dispute reason
      const modal = document.createElement('div');
      modal.className = 'modal';
      modal.id = 'dispute-modal';
      ModalManager.show('dispute-modal');

      modal.innerHTML = `
          <div class="modal-content">
              <span class="close-modal">&times;</span>
              <h2>Dispute Match</h2>
              <p>Please provide a reason for disputing this match:</p>
              <form id="dispute-form" enctype="multipart/form-data">
                  <div class="form-group">
                      <label for="player-name">Your Player Name:</label>
                      <input type="text" id="player-name" name="playerName" required placeholder="Enter your player name">
                  </div>
                  <div class="form-group">
                      <label for="dispute-reason">Reason:</label>
                      <textarea id="dispute-reason" name="reason" rows="4" required placeholder="Explain why this match result is incorrect..."></textarea>
                  </div>
                  <div class="form-group">
                      <label for="dispute-evidence">Evidence Screenshots (optional):</label>
                      <input type="file" id="dispute-evidence" name="evidence" accept="image/*" multiple>
                      <small>Upload screenshots as evidence to support your claim</small>
                  </div>
                  <input type="hidden" name="matchId" value="${matchId}">
                  <button type="submit" class="btn btn-primary">Submit Dispute</button>
              </form>
          </div>
      `;

      document.body.appendChild(modal);

      // Close button functionality
      const closeBtn = modal.querySelector('.close-modal');
      closeBtn.addEventListener('click', () => {
          document.body.removeChild(modal);
          modal.remove(); // Ensure it's fully removed
      });

      // Handle form submission
      const disputeForm = document.getElementById('dispute-form');
      disputeForm.addEventListener('submit', async (event) => {
          event.preventDefault();

          const formData = new FormData(disputeForm);

          if (!formData.get('playerName') || !formData.get('reason')) {
              alert('Please provide your player name and a reason for the dispute.');
              return;
          }

          try {
              // Show loading indicator
              const submitBtn = disputeForm.querySelector('button[type="submit"]');
              submitBtn.disabled = true;
              submitBtn.textContent = 'Submitting...';

              // Submit dispute to API
              const response = await fetch('/api/ladder/dispute-match', {
                  method: 'POST',
                  body: formData,
                  credentials: 'include'
              });

              const result = await response.json();

              if (!response.ok) {
                  throw new Error(result.error || 'Failed to submit dispute');
              }

              // Show success message
              alert('Dispute submitted successfully. An admin will review your request.');

              // Close modal
              document.body.removeChild(modal);
              modal.remove(); // Ensure it's fully removed

          } catch (error) {
              console.error('Error submitting dispute:', error);
              alert(`Error submitting dispute: ${error.message}`);

              // Re-enable submit button
              const submitBtn = disputeForm.querySelector('button[type="submit"]');
              submitBtn.disabled = false;
              submitBtn.textContent = 'Submit Dispute';
          }
      });

  } catch (error) {
      console.error('Error creating dispute modal:', error);
      alert('Error creating dispute form. Please try again later.');
  }
};

window.flagScreenshot = async function(screenshotUrl, screenshotId) {
  try {
      if (!confirm('Are you sure you want to flag this screenshot as inappropriate content? This action cannot be undone.')) {
          return;
      }

      const response = await fetch('/api/ladder/flag-screenshot', {
          method: 'POST',
          headers: {
              'Content-Type': 'application/json'
          },
          body: JSON.stringify({
              screenshotUrl,
              screenshotId
          }),
          credentials: 'include'
      });

      if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to flag screenshot');
      }

      alert('Screenshot has been flagged for review.');
  } catch (error) {
      console.error('Error flagging screenshot:', error);
      alert(`Error flagging screenshot: ${error.message}`);
  }
};

/**
 * Show player details in a modal using the new ModalManager
 */
window.openPlayerDetailsModal = async function(playerName) {
  if (!window.modalManager) {
    console.error('❌ ModalManager not available');
    return;
  }

  try {
    console.log(`🎭 Opening player details modal for ${playerName}`);
    
    // Show loading modal
    window.showLoadingModal('Loading player details...');
    
    // Load player details using the correct API endpoint that exists
    const response = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to load player');
    }
    
    const data = await response.json();
    console.log('🎯 Player modal API response:', data);
    
    const player = data.player || {};
    const stats = player.stats || {};
    
    console.log('🎯 Extracted player:', player);
    console.log('🎯 Extracted stats:', stats);
    
    const wins = stats.wins || 0;
    const losses = stats.losses || 0;
    const totalGames = stats.totalMatches || wins + losses;
    const winRate = totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;

    // Create modal content with tabs
    const modalContent = createPlayerModalContent(player);
    
    // Hide loading modal
    window.hideLoadingModal();
    
    // Create and show player details modal
    window.modalManager.createModal({
      id: 'player-stats-modal',
      content: modalContent,
      size: 'xl',
      showCloseButton: false
    });

    window.modalManager.show('player-stats-modal');
    
    // Reset modal size and ensure proper initial state
    setTimeout(() => {
      const modalContent = document.querySelector('#player-stats-modal .modal-content');
      if (modalContent) {
        modalContent.classList.remove('has-expanded-content');
      }
    }, 100);
    
    // Setup modal tabs and load initial data (pass player name instead of ID)
    setupPlayerModalTabs(playerName);
    
    console.log('✅ Player details modal opened successfully');
    
  } catch (error) {
    console.error('❌ Failed to show player details modal:', error);
    window.hideLoadingModal();
    window.showErrorModal(`Failed to load player details: ${error.message}`);
  }
};

/**
 * Create the player modal content structure
 */
function createPlayerModalContent(playerData) {
  // Debug rank data
  console.log('🎯 Creating player modal content with data:', playerData);
  console.log('🎯 Rank data:', playerData.rank);
  
  // Construct rank image path properly
  let rankImagePath = '/assets/img/ranks/emblem.png'; // default fallback
  if (playerData.rank?.image) {
    if (playerData.rank.image.startsWith('/assets') || playerData.rank.image.startsWith('http')) {
      rankImagePath = playerData.rank.image;
    } else {
      rankImagePath = `/assets/img/ranks/${playerData.rank.image}`;
    }
  }
  
  console.log('🎯 Final rank image path:', rankImagePath);
  
  // Get preferred race display
  const preferredRace = playerData.preferredRace || 'random';
  const isWC1 = playerData.gameType === 'war1' || playerData.gameType === 'warcraft1';
  const isWC3 = playerData.gameType === 'war3' || playerData.gameType === 'warcraft3';
  
  let raceDisplay = '';
  let raceIcon = '';
  
  if (isWC1 || playerData.gameType === 'war2' || playerData.gameType === 'warcraft2') {
    // WC1/WC2 races: human, orc, random
    switch (preferredRace) {
      case 'human':
        raceDisplay = 'Human';
        raceIcon = '👤';
        break;
      case 'orc':
        raceDisplay = 'Orc';
        raceIcon = '🟢';
        break;
      case 'random':
      default:
        raceDisplay = 'Random';
        raceIcon = '🎲';
        break;
    }
  } else if (isWC3) {
    // WC3 races: human, orc, undead, night_elf, random
    switch (preferredRace) {
      case 'human':
        raceDisplay = 'Human';
        raceIcon = '👤';
        break;
      case 'orc':
        raceDisplay = 'Orc';
        raceIcon = '🟢';
        break;
      case 'undead':
        raceDisplay = 'Undead';
        raceIcon = '💀';
        break;
      case 'night_elf':
        raceDisplay = 'Night Elf';
        raceIcon = '🌙';
        break;
      case 'random':
      default:
        raceDisplay = 'Random';
        raceIcon = '🎲';
        break;
    }
  } else {
    // Fallback for unknown game types
    raceDisplay = preferredRace.charAt(0).toUpperCase() + preferredRace.slice(1);
    raceIcon = '🎮';
  }
  
  return `
    <div class="player-modal-container">
      <!-- Player Header with Rank Image and Close Button -->
      <div class="player-modal-header">
        <div class="player-info">
          <div class="player-rank">
            <img src="${rankImagePath}" alt="Rank" class="rank-icon" onerror="this.src='/assets/img/ranks/emblem.png';">
          </div>
          <div class="player-details">
            <h3 class="player-name">${playerData.name || playerData.username}</h3>
            <div class="player-race-info">
              <span class="race-display">Preferred race ${raceDisplay}</span>
            </div>
            <div class="player-rank-info">
              <span class="rank-name">${playerData.rank?.name || 'Unranked'}</span>
            </div>
          </div>
          <div class="player-mmr">
            <span class="mmr-value">${playerData.mmr || 0}</span>
            <span class="mmr-label">MMR</span>
          </div>
        </div>
        <button class="close-modal" aria-label="Close modal" onclick="window.modalManager.hide('player-stats-modal')">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Modal Tabs -->
      <div class="modal-tabs">
        <button class="modal-tab active" data-tab="overview">Overview</button>
        <button class="modal-tab" data-tab="matches">Matches</button>
        <button class="modal-tab" data-tab="performance">Performance</button>
      </div>

      <!-- Tab Content -->
      <div class="modal-tab-content active" id="overview-content">
        <div class="loading">Loading overview...</div>
      </div>

      <div class="modal-tab-content" id="matches-content">
        <div class="loading">Loading matches...</div>
      </div>

      <div class="modal-tab-content" id="performance-content">
        <div class="loading">Loading performance data...</div>
      </div>
    </div>
  `;
}

/**
 * Setup modal tabs and event handlers
 */
function setupPlayerModalTabs(playerName) {
  const modal = document.getElementById('player-stats-modal');
  if (!modal) return;

  // Tab switching
  const tabs = modal.querySelectorAll('.modal-tab');
  const contents = modal.querySelectorAll('.modal-tab-content');

  tabs.forEach(tab => {
    tab.addEventListener('click', async () => {
      // Remove active class from all tabs and contents
      tabs.forEach(t => t.classList.remove('active'));
      contents.forEach(c => c.classList.remove('active'));

      // Add active class to clicked tab
      tab.classList.add('active');
      
      // Show corresponding content
      const tabName = tab.dataset.tab;
      const targetContent = modal.querySelector(`#${tabName}-content`);
      if (targetContent) {
        targetContent.classList.add('active');
        
        // Load tab data if not already loaded
        if (!targetContent.dataset.loaded) {
          await loadTabData(tabName, playerName, targetContent);
          targetContent.dataset.loaded = 'true';
        }
      }
    });
  });

  // Load initial overview tab
  const overviewTab = modal.querySelector('.modal-tab[data-tab="overview"]');
  if (overviewTab) {
    overviewTab.click();
  }
}

/**
 * Load data for a specific tab
 */
async function loadTabData(tabName, playerName, contentElement) {
  try {
    contentElement.innerHTML = '<div class="loading">Loading...</div>';
    
    // Get player data to determine game type
    let playerGameType = null;
    try {
      const playerResponse = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);
      if (playerResponse.ok) {
        const playerData = await playerResponse.json();
        playerGameType = playerData.player?.gameType || null;
      }
    } catch (error) {
      console.warn('Could not fetch player data for game type:', error);
    }
    
    switch (tabName) {
      case 'overview':
        await loadOverviewData(playerName, contentElement);
        break;
      case 'matches':
        await loadMatchesData(playerName, contentElement, 1, playerGameType);
        break;
      case 'performance':
        await loadPerformanceData(playerName, contentElement);
        break;
    }
  } catch (error) {
    console.error(`Error loading ${tabName} data:`, error);
    contentElement.innerHTML = `<div class="error-message">Failed to load ${tabName} data</div>`;
  }
}

/**
 * Load overview tab data
 */
async function loadOverviewData(playerName, contentElement) {
  try {
    // Use the correct API endpoint that exists
    const response = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);

    if (!response.ok) {
      throw new Error('Failed to load player data');
    }

    const data = await response.json();
    console.log('🎯 Player modal API response:', data);
    
    const player = data.player || {};
    const stats = player.stats || {};
    
    console.log('🎯 Extracted player:', player);
    console.log('🎯 Extracted stats:', stats);
    
    const wins = stats.wins || 0;
    const losses = stats.losses || 0;
    const totalGames = stats.totalMatches || wins + losses;
    const winRate = totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;

    // Check if this is a WC1 player for special handling
    const isWC1 = player.gameType === 'war1' || player.gameType === 'warcraft1';
    const isWC3 = player.gameType === 'war3' || player.gameType === 'warcraft3';
    const gameTypeDisplay = isWC1 ? 'Warcraft: Orcs & Humans' : 
                           player.gameType === 'war2' ? 'Warcraft II' :
                           player.gameType === 'war3' ? 'Warcraft III' : 
                           player.gameType || 'Unknown';

    // Get preferred race with proper formatting based on game type
    const preferredRace = player.preferredRace || 'random';
    let raceDisplay = '';
    let raceIcon = '';
    
    if (isWC1 || player.gameType === 'war2' || player.gameType === 'warcraft2') {
      // WC1/WC2 races: human, orc, random
      switch (preferredRace) {
        case 'human':
          raceDisplay = 'Human';
          raceIcon = '👤';
          break;
        case 'orc':
          raceDisplay = 'Orc';
          raceIcon = '🟢';
          break;
        case 'random':
        default:
          raceDisplay = 'Random';
          raceIcon = '🎲';
          break;
      }
    } else if (isWC3) {
      // WC3 races: human, orc, undead, night_elf, random
      switch (preferredRace) {
        case 'human':
          raceDisplay = 'Human';
          raceIcon = '👤';
          break;
        case 'orc':
          raceDisplay = 'Orc';
          raceIcon = '🟢';
          break;
        case 'undead':
          raceDisplay = 'Undead';
          raceIcon = '💀';
          break;
        case 'night_elf':
          raceDisplay = 'Night Elf';
          raceIcon = '🌙';
          break;
        case 'random':
        default:
          raceDisplay = 'Random';
          raceIcon = '🎲';
          break;
      }
    } else {
      // Fallback for unknown game types
      raceDisplay = preferredRace.charAt(0).toUpperCase() + preferredRace.slice(1);
      raceIcon = '🎮';
    }

    // Get most played race from stats if available
    let mostPlayedRace = 'Unknown';
    let mostPlayedIcon = '❓';
    if (stats.races) {
      const raceStats = stats.races;
      const raceCounts = Object.entries(raceStats)
        .filter(([race, count]) => count > 0)
        .sort(([,a], [,b]) => b - a);
      
      if (raceCounts.length > 0) {
        const [mostPlayed, count] = raceCounts[0];
        
        // Format most played race based on game type
        if (isWC1 || player.gameType === 'war2' || player.gameType === 'warcraft2') {
          switch (mostPlayed) {
            case 'human':
              mostPlayedRace = 'Human';
              mostPlayedIcon = '👤';
              break;
            case 'orc':
              mostPlayedRace = 'Orc';
              mostPlayedIcon = '🟢';
              break;
            case 'random':
              mostPlayedRace = 'Random';
              mostPlayedIcon = '🎲';
              break;
            default:
              mostPlayedRace = mostPlayed.charAt(0).toUpperCase() + mostPlayed.slice(1);
              mostPlayedIcon = '🎮';
          }
        } else if (isWC3) {
          switch (mostPlayed) {
            case 'human':
              mostPlayedRace = 'Human';
              mostPlayedIcon = '👤';
              break;
            case 'orc':
              mostPlayedRace = 'Orc';
              mostPlayedIcon = '🟢';
              break;
            case 'undead':
              mostPlayedRace = 'Undead';
              mostPlayedIcon = '💀';
              break;
            case 'night_elf':
              mostPlayedRace = 'Night Elf';
              mostPlayedIcon = '🌙';
              break;
            case 'random':
              mostPlayedRace = 'Random';
              mostPlayedIcon = '🎲';
              break;
            default:
              mostPlayedRace = mostPlayed.charAt(0).toUpperCase() + mostPlayed.slice(1);
              mostPlayedIcon = '🎮';
          }
        } else {
          mostPlayedRace = mostPlayed.charAt(0).toUpperCase() + mostPlayed.slice(1);
          mostPlayedIcon = '🎮';
        }
      }
    }

    // Create WC1-specific content if applicable
    const wc1Content = isWC1 ? `
      <div class="wc1-special-info">
        <div class="wc1-badge">
          <i class="fas fa-sword"></i> Warcraft: Orcs & Humans
        </div>
        <p class="wc1-description">
          This player competes in the classic Warcraft: Orcs & Humans ladder. 
          Matches are primarily vs AI campaigns and 1v1 battles.
        </p>
      </div>
    ` : '';

    contentElement.innerHTML = `
      <div class="overview-content">
        ${wc1Content}
        
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-value">${totalGames}</div>
              <div class="stat-label">Total Matches</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-value">${wins}</div>
              <div class="stat-label">Wins</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-value">${losses}</div>
              <div class="stat-label">Losses</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-content">
              <div class="stat-value ${winRate >= 60 ? 'positive' : winRate >= 40 ? '' : 'negative'}">${winRate}%</div>
              <div class="stat-label">Win Rate</div>
            </div>
          </div>
        </div>
      </div>
    `;

  } catch (error) {
    console.error('Error loading overview data:', error);
    contentElement.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Failed to Load Overview</h3>
        <p>Unable to load player overview for ${playerName}.</p>
        <p class="error-details">${error.message}</p>
      </div>
    `;
  }
}

/**
 * Load matches tab data
 */
async function loadMatchesData(playerName, contentElement, page = 1, gameType = null) {
  try {
    console.log(`🎯 Loading matches for player: ${playerName}, page: ${page}, gameType: ${gameType}`);
    
    // Show loading state
    contentElement.innerHTML = `
      <div class="matches-content">
        <div class="loading">
          <i class="fas fa-spinner fa-spin"></i>
          Loading match history...
        </div>
      </div>
    `;
    
    // Build query parameters
    const params = new URLSearchParams({
      limit: '5',
      page: page.toString()
    });
    
    // Add game type filter if provided
    if (gameType && gameType !== 'all') {
      params.append('gameType', gameType);
    }
    
    // Use the dedicated matches endpoint for better pagination and filtering
    const response = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}/matches?${params}`);
    
    if (!response.ok) {
      throw new Error('Failed to load matches data');
    }

    const data = await response.json();
    const matches = data.matches || [];
    const pagination = data.pagination || {};
    
    console.log('🎯 Player matches API response:', data);
    console.log('🎯 Total matches found:', matches.length);
    console.log('🎯 Pagination info:', pagination);
    console.log('🎯 Raw pagination data:', JSON.stringify(pagination, null, 2));

    if (matches.length === 0) {
      contentElement.innerHTML = `
        <div class="matches-content">
          <div class="no-data">
            <i class="fas fa-history"></i>
            <h3>No Recent Matches</h3>
            <p>No match history found for ${playerName}${gameType ? ` in ${gameType}` : ''}.</p>
            <p class="help-text">Matches will appear here once they are reported and verified.</p>
          </div>
        </div>
      `;
      return;
    }

    // Create matches list HTML
    const matchesHtml = matches.map(match => {
      const matchDate = new Date(match.date || match.createdAt);
      const formattedDate = matchDate.toLocaleDateString();
      const timeAgo = getTimeAgo(matchDate);
      
      // Get match outcome from the processed match data
      const outcome = match.outcome || 'unknown';
      const mmrChange = match.mmrChange || 0;
      
      console.log(`🎮 Match: ${match.map?.name || match.mapName} - ${outcome.toUpperCase()} - ${timeAgo} - MMR: ${mmrChange}`);
      console.log('🎮 Full match data:', match);
      
      const outcomeClass = outcome === 'win' ? 'match-win' : outcome === 'loss' ? 'match-loss' : 'match-draw';
      const outcomeIcon = outcome === 'win' ? 'trophy' : outcome === 'loss' ? 'times' : 'equals';
      
      // Get all players and determine display logic
      const allPlayers = match.players || [];
      const matchType = match.matchType || '1v1';
      const gameType = match.gameType || 'unknown';
      const isTeamMatch = matchType.includes('v') && parseInt(matchType.split('v')[0]) > 1;
      
      // For WC1 matches, show special handling
      const isWC1 = gameType === 'warcraft1' || gameType === 'war1';
      
      console.log('🎮 Players data:', {
        allPlayers,
        matchType,
        gameType,
        isTeamMatch,
        isWC1
      });
      
      // Create player links for all players in the match
      let playersDisplay;
      if (isWC1 && matchType === 'vsai') {
        // WC1 vs AI match - show player vs AI
        const humanPlayer = allPlayers.find(p => !p.isAI);
        const humanPlayerName = humanPlayer ? (humanPlayer.playerId?.name || humanPlayer.name) : 'Unknown';
        const isWinner = outcome === 'win';
        
        playersDisplay = `
          <div class="match-players">
            <span class="player-link ${isWinner ? 'winner' : 'loser'}" onclick="window.handlePlayerLinkClick('${humanPlayerName}', event)" title="View ${humanPlayerName}'s profile">
              <i class="fas fa-user"></i> ${humanPlayerName}
              ${isWinner ? ' <i class="fas fa-trophy winner-icon" title="Winner"></i>' : ' <i class="fas fa-times loser-icon" title="Loser"></i>'}
            </span>
            <span class="vs-separator">vs</span>
            <span class="ai-player ${!isWinner ? 'winner' : 'loser'}">
              <i class="fas fa-robot"></i> AI
              ${!isWinner ? ' <i class="fas fa-trophy winner-icon" title="Winner"></i>' : ' <i class="fas fa-times loser-icon" title="Loser"></i>'}
            </span>
          </div>
        `;
      } else if (!isTeamMatch) {
        // 1v1 match - show both players as clickable links
        const player1 = allPlayers[0];
        const player2 = allPlayers[1];
        
        if (player1 && player2) {
          const player1Name = player1.playerId?.name || player1.name || 'Unknown';
          const player2Name = player2.playerId?.name || player2.name || 'Unknown';
          
          // Determine winner/loser based on match outcome
          const player1IsWinner = player1Name === playerName ? outcome === 'win' : outcome === 'loss';
          const player2IsWinner = player2Name === playerName ? outcome === 'win' : outcome === 'loss';
          
          playersDisplay = `
            <div class="match-players">
              <span class="player-link ${player1IsWinner ? 'winner' : 'loser'}" onclick="window.handlePlayerLinkClick('${player1Name}', event)" title="View ${player1Name}'s profile">
                <i class="fas fa-user"></i> ${player1Name}
                ${player1IsWinner ? ' <i class="fas fa-trophy winner-icon" title="Winner"></i>' : ' <i class="fas fa-times loser-icon" title="Loser"></i>'}
              </span>
              <span class="vs-separator">vs</span>
              <span class="player-link ${player2IsWinner ? 'winner' : 'loser'}" onclick="window.handlePlayerLinkClick('${player2Name}', event)" title="View ${player2Name}'s profile">
                <i class="fas fa-user"></i> ${player2Name}
                ${player2IsWinner ? ' <i class="fas fa-trophy winner-icon" title="Winner"></i>' : ' <i class="fas fa-times loser-icon" title="Loser"></i>'}
              </span>
            </div>
          `;
        } else {
          // Fallback for incomplete player data
          playersDisplay = `<span class="match-opponents">vs ${allPlayers.find(p => (p.playerId?.name || p.name) !== playerName)?.playerId?.name || 'Unknown'}</span>`;
        }
      } else {
        // Team match - organize players by teams
        const teams = {};
        const currentPlayerName = playerName;
        
        // Group players by team
        allPlayers.forEach(p => {
          const playerName = p.playerId?.name || p.name || 'Unknown';
          const team = p.team || 0;
          const isCurrentPlayer = playerName === currentPlayerName;
          
          if (!teams[team]) {
            teams[team] = [];
          }
          
          teams[team].push({
            name: playerName,
            isCurrentPlayer,
            isAI: p.isAI || false
          });
        });
        
        // Determine winning team based on match outcome
        const currentPlayerTeam = allPlayers.find(p => (p.playerId?.name || p.name) === currentPlayerName)?.team || 0;
        const currentPlayerWon = outcome === 'win';
        const winningTeam = currentPlayerWon ? currentPlayerTeam : (currentPlayerTeam === 1 ? 2 : 1);
        
        // Create team displays
        const teamDisplays = Object.keys(teams).sort().map(teamNum => {
          const team = teams[teamNum];
          const isWinningTeam = parseInt(teamNum) === winningTeam;
          const teamClass = isWinningTeam ? 'winning-team' : 'losing-team';
          
          const playersList = team.map(player => {
            const playerClass = player.isCurrentPlayer ? 'current-player' : 'player-link';
            const winnerLoserClass = isWinningTeam ? 'winner' : 'loser';
            
            return `
              <span class="${playerClass} ${winnerLoserClass}" onclick="window.handlePlayerLinkClick('${player.name}', event)" title="View ${player.name}'s profile">
                <i class="fas fa-user"></i> ${player.name}
                ${player.isCurrentPlayer ? ' <i class="fas fa-star" title="You"></i>' : ''}
                ${isWinningTeam ? ' <i class="fas fa-trophy winner-icon" title="Winner"></i>' : ' <i class="fas fa-times loser-icon" title="Loser"></i>'}
              </span>
            `;
          }).join('<span class="player-separator">•</span>');
          
          return `
            <div class="team ${teamClass}">
              <div class="team-header">
                <span class="team-label">Team ${teamNum}</span>
                ${isWinningTeam ? '<span class="team-result winner">Winner</span>' : '<span class="team-result loser">Loser</span>'}
              </div>
              <div class="team-players">
                ${playersList}
              </div>
            </div>
          `;
        }).join('<div class="team-vs-separator">VS</div>');
        
        playersDisplay = `
          <div class="match-players team-match">
            ${teamDisplays}
            <div class="match-type-badge">${matchType}</div>
          </div>
        `;
      }

      // Add WC1-specific styling and information
      const wc1Badge = isWC1 ? `<span class="wc1-badge">WC1</span>` : '';
      const gameTypeIcon = isWC1 ? '🗡️' : gameType === 'warcraft2' ? '⚔️' : gameType === 'warcraft3' ? '🏰' : '🎮';

      const matchHtml = `
        <div class="match-item ${outcomeClass}" data-match-id="${match._id || match.id}" onclick="window.toggleMatchDetails('${match._id || match.id}', event)" title="Click to view detailed match information">
          <div class="match-header">
            <div class="match-outcome">
              <i class="fas fa-${outcomeIcon}"></i>
              <span class="outcome-text">${outcome.toUpperCase()}</span>
            </div>
            <div class="match-type">
              ${gameTypeIcon} ${matchType}
              ${wc1Badge}
            </div>
            <div class="match-date" title="${formattedDate}">${timeAgo}</div>
            <div class="match-expand-icon">
              <i class="fas fa-external-link-alt" title="View Details"></i>
            </div>
          </div>
          <div class="match-details">
            <div class="match-map">
              <i class="fas fa-map"></i>
              <span>${match.map?.name || match.mapName || 'Unknown Map'}</span>
            </div>
            <div class="match-players-container">
              ${playersDisplay}
            </div>
            ${mmrChange !== 0 ? `
              <div class="mmr-change ${mmrChange > 0 ? 'positive' : 'negative'}">
                <i class="fas fa-chart-line"></i>
                <span>${mmrChange > 0 ? '+' : ''}${mmrChange} MMR</span>
              </div>
            ` : ''}
          </div>
          <div class="match-details-expanded" style="display: none;">
            <div class="match-details-content">
              <div class="match-details-section">
                <h4><i class="fas fa-users"></i> Players & Teams</h4>
                <div class="match-players-detailed">
                  ${generateDetailedPlayersDisplay(allPlayers, playerName, outcome, matchType, isWC1)}
                </div>
              </div>
              <div class="match-details-section">
                <h4><i class="fas fa-map-marked-alt"></i> Match Information</h4>
                <div class="match-info-grid">
                  <div class="match-info-item">
                    <span class="info-label">Map:</span>
                    <span class="info-value">${match.map?.name || match.mapName || 'Unknown Map'}</span>
                  </div>
                  <div class="match-info-item">
                    <span class="info-label">Game Type:</span>
                    <span class="info-value">${gameType.toUpperCase()}</span>
                  </div>
                  <div class="match-info-item">
                    <span class="info-label">Match Type:</span>
                    <span class="info-value">${matchType}</span>
                  </div>
                  <div class="match-info-item">
                    <span class="info-label">Date:</span>
                    <span class="info-value">${formattedDate}</span>
                  </div>
                  ${match.duration ? `
                    <div class="match-info-item">
                      <span class="info-label">Duration:</span>
                      <span class="info-value">${formatDuration(match.duration)}</span>
                    </div>
                  ` : ''}
                  ${mmrChange !== 0 ? `
                    <div class="match-info-item">
                      <span class="info-label">MMR Change:</span>
                      <span class="info-value ${mmrChange > 0 ? 'positive' : 'negative'}">${mmrChange > 0 ? '+' : ''}${mmrChange}</span>
                    </div>
                  ` : ''}
                </div>
              </div>
              ${match.resources || match.stats ? `
                <div class="match-details-section">
                  <h4><i class="fas fa-chart-bar"></i> Match Statistics</h4>
                  <div class="match-stats-grid">
                    ${generateMatchStats(match)}
                  </div>
                </div>
              ` : ''}
            </div>
          </div>
        </div>
      `;

      console.log('🎯 Generated match HTML length:', matchHtml.length);
      console.log('🎯 Match HTML contains expanded content:', matchHtml.includes('match-details-expanded'));
      
      return matchHtml;
    }).join('');

    // Create pagination controls
    const currentPage = pagination.page || page;
    const totalPages = pagination.pages || 1;
    const totalMatches = pagination.total || matches.length;
    
    console.log('📊 Pagination data:', { currentPage, totalPages, totalMatches, matchesCount: matches.length });
    
    // Fallback calculation if backend returns incorrect pagination
    const calculatedTotalPages = Math.ceil(totalMatches / 5);
    const finalTotalPages = totalPages > 1 ? totalPages : calculatedTotalPages;
    
    console.log('📊 Calculated pagination:', { calculatedTotalPages, finalTotalPages });
    
    // Always show pagination info, even for single page
    const startMatch = ((currentPage - 1) * 5) + 1;
    const endMatch = Math.min(currentPage * 5, totalMatches);
    
    let paginationHtml = `
      <div class="matches-pagination">
        <div class="pagination-info">
          <span class="matches-range">Showing ${startMatch}-${endMatch} of ${totalMatches} matches</span>
          <span class="page-info">Page ${currentPage} of ${finalTotalPages}</span>
        </div>
    `;
    
    // Only show pagination controls if there are multiple pages
    if (finalTotalPages > 1) {
      paginationHtml += `
        <div class="pagination-controls">
          <button class="btn btn-secondary" onclick="window.loadMatchesPage('${playerName}', ${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>
            <i class="fas fa-chevron-left"></i> Previous
          </button>
          <div class="page-numbers">
            ${generatePageNumbers(currentPage, finalTotalPages, playerName)}
          </div>
          <button class="btn btn-secondary" onclick="window.loadMatchesPage('${playerName}', ${currentPage + 1})" ${currentPage >= finalTotalPages ? 'disabled' : ''}>
            Next <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      `;
    }
    
    paginationHtml += '</div>';

    contentElement.innerHTML = `
      <div class="matches-content">
        <div class="matches-list">
          ${matchesHtml}
        </div>
        ${paginationHtml}
      </div>
    `;

  } catch (error) {
    console.error('Error loading matches data:', error);
    contentElement.innerHTML = `
      <div class="matches-content">
        <div class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <h3>Failed to Load Matches</h3>
          <p>Unable to load match history for ${playerName}.</p>
          <p class="error-details">${error.message}</p>
          <button class="btn btn-primary" onclick="window.loadMatchesPage('${playerName}', 1)">
            <i class="fas fa-refresh"></i> Try Again
          </button>
        </div>
      </div>
    `;
  }
}

/**
 * Generate page number buttons for pagination
 */
function generatePageNumbers(currentPage, totalPages, playerName) {
  const pages = [];
  const maxVisible = 5;
  
  if (totalPages <= maxVisible) {
    // Show all pages if total is small
    for (let i = 1; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Show smart pagination with ellipsis
    if (currentPage <= 3) {
      // Near start: show 1, 2, 3, 4, 5, ..., last
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      if (totalPages > 5) {
        pages.push('...');
        pages.push(totalPages);
      }
    } else if (currentPage >= totalPages - 2) {
      // Near end: show 1, ..., last-4, last-3, last-2, last-1, last
      pages.push(1);
      pages.push('...');
      for (let i = totalPages - 4; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Middle: show 1, ..., current-1, current, current+1, ..., last
      pages.push(1);
      pages.push('...');
      for (let i = currentPage - 1; i <= currentPage + 1; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages);
    }
  }
  
  return pages.map(page => {
    if (page === '...') {
      return '<span class="page-ellipsis">...</span>';
    }
    const isCurrent = page === currentPage;
    return `
      <button class="btn ${isCurrent ? 'btn-primary' : 'btn-secondary'}" 
              onclick="window.loadMatchesPage('${playerName}', ${page})"
              ${isCurrent ? 'disabled' : ''}>
        ${page}
      </button>
    `;
  }).join('');
}

/**
 * Load matches for a specific page (for pagination)
 */
window.loadMatchesPage = async function(playerName, page) {
  const modal = document.getElementById('player-stats-modal');
  if (!modal) return;
  
  const matchesContent = modal.querySelector('#matches-content');
  if (!matchesContent) return;
  
  // Store current page in the content element for reference
  matchesContent.dataset.currentPage = page;
  
  // Try to get player game type from the modal or fetch it
  let playerGameType = null;
  try {
    // First try to get from modal data
    const playerResponse = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);
    if (playerResponse.ok) {
      const playerData = await playerResponse.json();
      playerGameType = playerData.player?.gameType || null;
    }
  } catch (error) {
    console.warn('Could not fetch player data for game type:', error);
  }
  
  // Update the content element with new page data
  await loadMatchesData(playerName, matchesContent, page, playerGameType);
};

/**
 * Handle player link clicks with proper error handling
 */
window.handlePlayerLinkClick = function(playerName, event) {
  event.preventDefault();
  event.stopPropagation();
  
  console.log(`🎯 Player link clicked: ${playerName}`);
  
  try {
    // Try to open player details modal
    if (window.showPlayerDetails) {
      window.showPlayerDetails(playerName);
    } else if (window.openPlayerDetailsModal) {
      window.openPlayerDetailsModal(playerName);
    } else {
      // Fallback: show alert with player info
      alert(`Player: ${playerName}\n\nPlayer modal not available. Please refresh the page and try again.`);
    }
  } catch (error) {
    console.error('Error opening player modal:', error);
    alert(`Unable to open player profile for ${playerName}. Please try again.`);
  }
};

/**
 * Ensure player modal functions are available globally
 */
window.showPlayerDetails = window.showPlayerDetails || window.openPlayerDetailsModal || function(playerName) {
  console.warn('Player modal function not available, using fallback');
  alert(`Player: ${playerName}\n\nPlayer modal system not loaded. Please refresh the page.`);
};

/**
 * Get time ago from date
 */
function getTimeAgo(date) {
  const now = new Date();
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMins / 60);
  const diffDays = Math.floor(diffHours / 24);

  if (diffMins < 1) return 'Just now';
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  
  return date.toLocaleDateString();
}

// ... existing code ...

// Close the conditional block for profile page only
} else {
  console.log('🚫 PlayerDetails.js skipped - not on profile page');
}

// Add CSS styles for the player modal
const modalStyles = `
<style>
/* Player Modal Styles */
.player-modal-container {
  max-width: 100%;
  padding: 0;
}

.player-modal-header {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.1), rgba(184, 134, 11, 0.1));
  padding: 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin: -1rem -1rem 0 -1rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.player-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.player-name {
  font-size: 1.5rem;
  margin: 0;
  color: #daa520;
  font-weight: bold;
}

.player-rank {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.rank-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 4px;
}

.rank-name {
  color: #daa520;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.player-mmr {
  margin-left: auto;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 80px;
}

.mmr-value {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #daa520;
  line-height: 1;
}

.mmr-label {
  font-size: 0.8rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 0.25rem;
}

/* Close Button in Player Header */
.player-modal-header .close-modal {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #888;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  margin-left: 1rem;
  flex-shrink: 0;
}

.player-modal-header .close-modal:hover {
  background: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #f44336;
  transform: scale(1.1);
}

.player-modal-header .close-modal:active {
  transform: scale(0.95);
}

.player-modal-header .close-modal i {
  font-size: 1rem;
}

/* Modal Tabs */
.modal-tabs {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin: 1rem 0;
}

.modal-tab {
  background: none;
  border: none;
  color: #888;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.modal-tab:hover {
  color: #daa520;
}

.modal-tab.active {
  color: #daa520;
  border-bottom-color: #daa520;
}

/* Tab Content */
.modal-tab-content {
  display: none;
  padding: 1rem 0;
}

.modal-tab-content.active {
  display: block;
}

/* WC1 Specific Styles */
.wc1-badge {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: #FFD700;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: 8px;
  text-transform: uppercase;
  border: 1px solid #DAA520;
}

.wc1-ai-match {
  color: #FFD700;
  font-weight: bold;
  background: rgba(139, 69, 19, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #DAA520;
}

/* Matches Tab Styles */
.matches-content {
  max-height: 500px;
  overflow-y: auto;
}

.matches-header {
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.matches-header h3 {
  color: #daa520;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.matches-header p {
  color: #888;
  margin: 0.25rem 0;
  font-size: 0.9rem;
}

.pagination-info {
  color: #daa520 !important;
  font-weight: bold;
}

.matches-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.match-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.match-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(218, 165, 32, 0.3);
}

.match-item.match-win {
  border-left: 4px solid #4CAF50;
}

.match-item.match-loss {
  border-left: 4px solid #f44336;
}

.match-item.match-draw {
  border-left: 4px solid #FF9800;
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.match-outcome {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: bold;
}

.match-outcome .fa-trophy {
  color: #FFD700;
}

.match-outcome .fa-times {
  color: #f44336;
}

.match-outcome .fa-equals {
  color: #FF9800;
}

.match-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #888;
  font-size: 0.9rem;
}

.match-date {
  color: #666;
  font-size: 0.8rem;
}

.match-details {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 1rem;
  align-items: center;
}

.match-map, .match-opponents {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ccc;
  font-size: 0.9rem;
}

.mmr-change {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: bold;
  font-size: 0.9rem;
}

.mmr-change.positive {
  color: #4CAF50;
}

.mmr-change.negative {
  color: #f44336;
}

/* Pagination Styles */
.matches-pagination {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #888;
  font-size: 0.9rem;
}

.matches-range {
  font-weight: 500;
}

.page-info {
  color: #daa520;
  font-weight: bold;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.page-numbers .btn {
  min-width: 40px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  border-radius: 6px;
}

.page-numbers .btn.btn-primary {
  background: rgba(218, 165, 32, 0.3);
  border-color: #daa520;
  color: #daa520;
  cursor: default;
}

.page-numbers .btn.btn-primary:hover {
  background: rgba(218, 165, 32, 0.3);
  transform: none;
}

.page-ellipsis {
  color: #888;
  font-weight: bold;
  padding: 0 0.5rem;
  user-select: none;
}

.pagination-controls .btn {
  background: rgba(218, 165, 32, 0.2);
  border: 1px solid #daa520;
  color: #daa520;
  padding: 0.75rem 1.25rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
  justify-content: center;
}

.pagination-controls .btn:hover:not(:disabled) {
  background: rgba(218, 165, 32, 0.3);
  border-color: #FFD700;
  color: #FFD700;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(218, 165, 32, 0.2);
}

.pagination-controls .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(128, 128, 128, 0.1);
  border-color: #666;
  color: #666;
}

.pagination-controls .btn:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Enhanced Loading States */
.loading {
  text-align: center;
  padding: 3rem 2rem;
  color: #888;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading i {
  font-size: 2rem;
  color: #daa520;
  animation: spin 1s linear infinite;
}

.loading::after {
  display: none; /* Remove the old loading animation */
}

/* Optimized Match List */
.matches-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-height: 200px; /* Prevent layout shift during loading */
}

.matches-list:empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  color: #888;
  font-style: italic;
}

/* Enhanced Error States */
.error-message {
  text-align: center;
  padding: 3rem 2rem;
  color: #888;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.error-message i {
  font-size: 3rem;
  color: #f44336;
  opacity: 0.7;
}

.error-message h3 {
  color: #f44336;
  margin: 0;
  font-size: 1.3rem;
}

.error-message p {
  margin: 0.5rem 0;
  max-width: 400px;
  line-height: 1.4;
}

.error-message .btn {
  margin-top: 1rem;
  background: rgba(218, 165, 32, 0.2);
  border: 1px solid #daa520;
  color: #daa520;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.error-message .btn:hover {
  background: rgba(218, 165, 32, 0.3);
  border-color: #FFD700;
  color: #FFD700;
  transform: translateY(-1px);
}

/* Performance Optimizations */
.match-item {
  will-change: transform;
  contain: layout style paint;
}

.match-players {
  contain: layout style;
}

.player-link {
  contain: layout style paint;
}

/* Responsive Pagination */
@media (max-width: 768px) {
  .pagination-info {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
  
  .pagination-controls {
    flex-direction: column;
    gap: 1rem;
  }
  
  .page-numbers {
    order: 2;
    justify-content: center;
  }
  
  .pagination-controls .btn {
    min-width: 100px;
    order: 1;
  }
  
  .page-numbers .btn {
    min-width: 35px;
    height: 35px;
    font-size: 0.9rem;
  }
  
  .matches-list {
    min-height: 150px;
  }
}

@media (max-width: 480px) {
  .page-numbers {
    gap: 0.25rem;
  }
  
  .page-numbers .btn {
    min-width: 30px;
    height: 30px;
    font-size: 0.8rem;
  }
  
  .pagination-controls .btn {
    min-width: 80px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }
}

/* No Data and Error States */
.no-data, .error-message {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.no-data i, .error-message i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.no-data h3, .error-message h3 {
  color: #daa520;
  margin: 0 0 0.5rem 0;
}

.help-text {
  font-size: 0.8rem;
  color: #666;
  margin-top: 0.5rem;
}

.error-details {
  font-size: 0.8rem;
  color: #f44336;
  margin-top: 0.5rem;
  font-family: monospace;
}

/* Team Match Link */
.team-match-link {
  color: #daa520;
  cursor: pointer;
  text-decoration: underline;
}

.team-match-link:hover {
  color: #FFD700;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 2rem;
  color: #888;
}

.loading::after {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #daa520;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  margin-left: 0.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Match Players Display */
.match-players-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ccc;
  font-size: 0.9rem;
}

.match-players {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.player-link {
  color: #daa520;
  cursor: pointer;
  text-decoration: none;
  padding: 2px 6px;
  border-radius: 4px;
  background: rgba(218, 165, 32, 0.1);
  border: 1px solid rgba(218, 165, 32, 0.3);
  transition: all 0.3s ease;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
}

.player-link:hover {
  background: rgba(218, 165, 32, 0.2);
  border-color: #daa520;
  color: #FFD700;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(218, 165, 32, 0.2);
}

.player-link i {
  font-size: 0.8rem;
  opacity: 0.8;
}

.current-player {
  color: #FFD700;
  background: rgba(255, 215, 0, 0.15);
  border: 1px solid rgba(255, 215, 0, 0.4);
  font-weight: bold;
}

.current-player:hover {
  background: rgba(255, 215, 0, 0.25);
  border-color: #FFD700;
  color: #FFF;
}

.vs-separator {
  color: #888;
  font-weight: bold;
  margin: 0 0.5rem;
  font-size: 0.8rem;
}

.ai-player {
  color: #888;
  background: rgba(128, 128, 128, 0.1);
  border: 1px solid rgba(128, 128, 128, 0.3);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  gap: 0.3rem;
  font-style: italic;
}

.ai-player i {
  color: #666;
  font-size: 0.8rem;
}

.player-separator {
  color: #666;
  margin: 0 0.3rem;
  font-size: 0.8rem;
}

/* Team Match Styles */
.match-players.team-match {
  flex-direction: column;
  align-items: flex-start;
  gap: 0.5rem;
}

.players-list {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.match-type-badge {
  background: rgba(139, 69, 19, 0.2);
  color: #CD853F;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  border: 1px solid rgba(139, 69, 19, 0.4);
  text-transform: uppercase;
}

/* Winner/Loser Indicators */
.player-link.winner {
  background: rgba(76, 175, 80, 0.15);
  border-color: rgba(76, 175, 80, 0.4);
  color: #4CAF50;
}

.player-link.winner:hover {
  background: rgba(76, 175, 80, 0.25);
  border-color: #4CAF50;
  color: #66BB6A;
}

.player-link.loser {
  background: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.3);
  color: #f44336;
}

.player-link.loser:hover {
  background: rgba(244, 67, 54, 0.2);
  border-color: #f44336;
  color: #EF5350;
}

.ai-player.winner {
  background: rgba(76, 175, 80, 0.1);
  border-color: rgba(76, 175, 80, 0.3);
  color: #4CAF50;
}

.ai-player.loser {
  background: rgba(244, 67, 54, 0.05);
  border-color: rgba(244, 67, 54, 0.2);
  color: #f44336;
}

.winner-icon {
  color: #FFD700;
  margin-left: 0.3rem;
  font-size: 0.8rem;
}

.loser-icon {
  color: #f44336;
  margin-left: 0.3rem;
  font-size: 0.8rem;
}

/* Team Organization Styles */
.team {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.team.winning-team {
  background: rgba(76, 175, 80, 0.05);
  border-color: rgba(76, 175, 80, 0.3);
  box-shadow: 0 0 10px rgba(76, 175, 80, 0.1);
}

.team.losing-team {
  background: rgba(244, 67, 54, 0.03);
  border-color: rgba(244, 67, 54, 0.2);
  opacity: 0.8;
}

.team-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.team-label {
  font-weight: bold;
  color: #daa520;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.team-result {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.team-result.winner {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.4);
}

.team-result.loser {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

.team-players {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.team-vs-separator {
  text-align: center;
  color: #daa520;
  font-weight: bold;
  font-size: 1.2rem;
  margin: 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 0 10px rgba(218, 165, 32, 0.5);
}

/* Enhanced Team Match Layout */
.match-players.team-match {
  flex-direction: column;
  align-items: stretch;
  gap: 0.5rem;
}

.match-players.team-match .team {
  margin-bottom: 0;
}

.match-players.team-match .team:last-child {
  margin-bottom: 0.5rem;
}

/* Current Player Enhancement */
.current-player.winner {
  background: rgba(255, 215, 0, 0.2);
  border-color: rgba(255, 215, 0, 0.5);
  color: #FFD700;
  font-weight: bold;
}

.current-player.loser {
  background: rgba(244, 67, 54, 0.15);
  border-color: rgba(244, 67, 54, 0.4);
  color: #f44336;
  font-weight: bold;
}

/* Responsive Team Layout */
@media (max-width: 768px) {
  .team-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
  
  .team-players {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
  
  .player-separator {
    display: none;
  }
  
  .team-vs-separator {
    font-size: 1rem;
    margin: 0.3rem 0;
  }
}

/* Overview Tab Styles */
.overview-content {
  padding: 1rem 0;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(218, 165, 32, 0.3);
  transform: translateY(-2px);
}

.stat-content {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #daa520;
}

.stat-value.positive {
  color: #4CAF50;
}

.stat-value.negative {
  color: #f44336;
}

.stat-label {
  font-size: 0.9rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Player Details Section */
.player-details-section {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.player-details-section h3 {
  color: #daa520;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 6px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-label {
  color: #888;
  font-size: 0.9rem;
  font-weight: 500;
}

.detail-value {
  color: #fff;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Race Display Styles */
.detail-value .race-icon {
  font-size: 1.2rem;
}

/* Match Type Stats */
.match-type-stats {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
}

.match-type-stats h3 {
  color: #daa520;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.match-type-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.match-type-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.3s ease;
}

.match-type-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(218, 165, 32, 0.3);
}

.match-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.match-type-name {
  color: #daa520;
  font-weight: bold;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.match-type-count {
  color: #888;
  font-size: 0.8rem;
}

.match-type-stats {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.match-type-stats .wins {
  color: #4CAF50;
  font-weight: bold;
}

.match-type-stats .losses {
  color: #f44336;
  font-weight: bold;
}

.match-type-stats .win-rate {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.match-type-stats .win-rate.excellent {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.match-type-stats .win-rate.average {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
  border: 1px solid rgba(255, 152, 0, 0.3);
}

.match-type-stats .win-rate.poor {
  background: rgba(244, 67, 54, 0.2);
  color: #f44336;
  border: 1px solid rgba(244, 67, 54, 0.3);
}

/* WC1 Special Info Styles */
.wc1-special-info {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(160, 82, 45, 0.1));
  border: 1px solid rgba(139, 69, 19, 0.3);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.wc1-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(139, 69, 19, 0.2);
  color: #CD853F;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
}

.wc1-badge i {
  font-size: 1rem;
}

.wc1-description {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .match-type-grid {
    grid-template-columns: 1fr;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .match-type-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
  
  .match-type-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Responsive Design for Player Header */
@media (max-width: 768px) {
  .player-modal-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem;
  }
  
  .player-info {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.75rem;
  }
  
  .player-name {
    font-size: 1.3rem;
    order: 1;
  }
  
  .player-rank {
    order: 2;
    margin: 0;
  }
  
  .player-mmr {
    order: 3;
    margin: 0;
    min-width: 100px;
  }
  
  .player-modal-header .close-modal {
    position: absolute;
    top: 1rem;
    right: 1rem;
    margin: 0;
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .player-modal-header .close-modal i {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .player-modal-header {
    padding: 0.75rem;
  }
  
  .player-name {
    font-size: 1.2rem;
  }
  
  .player-rank {
    padding: 0.4rem 0.6rem;
  }
  
  .rank-icon {
    width: 28px;
    height: 28px;
  }
  
  .rank-name {
    font-size: 0.8rem;
  }
  
  .player-mmr {
    padding: 0.6rem 0.8rem;
    min-width: 80px;
  }
  
  .mmr-value {
    font-size: 1.5rem;
  }
  
  .mmr-label {
    font-size: 0.7rem;
  }
  
  .player-modal-header .close-modal {
    width: 30px;
    height: 30px;
    top: 0.75rem;
    right: 0.75rem;
  }
  
  .player-modal-header .close-modal i {
    font-size: 0.8rem;
  }
}
</style>
`;

// Add styles to the document head
if (!document.getElementById('player-modal-styles')) {
  const styleElement = document.createElement('div');
  styleElement.id = 'player-modal-styles';
  styleElement.innerHTML = modalStyles;
  document.head.appendChild(styleElement);
}

/**
 * Generate detailed players display for expanded match view
 */
function generateDetailedPlayersDisplay(allPlayers, currentPlayerName, outcome, matchType, isWC1) {
  console.log('🔍 Generating detailed players display:', {
    allPlayers,
    currentPlayerName,
    outcome,
    matchType,
    isWC1
  });

  // If no players data, show a fallback
  if (!allPlayers || allPlayers.length === 0) {
    return `
      <div class="detailed-players">
        <div class="no-detailed-data">
          <i class="fas fa-info-circle"></i>
          <p>No detailed player information available for this match.</p>
          <p class="match-summary">Match Type: ${matchType} | Outcome: ${outcome.toUpperCase()}</p>
        </div>
      </div>
    `;
  }

  if (isWC1 && matchType === 'vsai') {
    // WC1 vs AI match
    const humanPlayer = allPlayers.find(p => !p.isAI);
    const humanPlayerName = humanPlayer ? (humanPlayer.playerId?.name || humanPlayer.name) : 'Unknown';
    const isWinner = outcome === 'win';
    
    return `
      <div class="detailed-players">
        <div class="player-detail ${isWinner ? 'winner' : 'loser'}">
          <div class="player-detail-header">
            <i class="fas fa-user"></i>
            <span class="player-name">${humanPlayerName}</span>
            ${isWinner ? '<span class="result-badge winner">Winner</span>' : '<span class="result-badge loser">Loser</span>'}
          </div>
          <div class="player-detail-info">
            <span class="player-race">${humanPlayer?.race || 'Unknown'} Race</span>
            ${humanPlayer?.resources ? `<span class="player-resources">Resources: ${humanPlayer.resources}</span>` : ''}
          </div>
        </div>
        <div class="vs-separator-detailed">VS</div>
        <div class="player-detail ${!isWinner ? 'winner' : 'loser'}">
          <div class="player-detail-header">
            <i class="fas fa-robot"></i>
            <span class="player-name">AI</span>
            ${!isWinner ? '<span class="result-badge winner">Winner</span>' : '<span class="result-badge loser">Loser</span>'}
          </div>
          <div class="player-detail-info">
            <span class="player-race">AI Opponent</span>
          </div>
        </div>
      </div>
    `;
  }
  
  // Handle FFA (Free For All) matches differently
  if (matchType === 'ffa' || matchType === 'free-for-all') {
    // For FFA, each player is their own "team" and we need to determine the winner
    const currentPlayer = allPlayers.find(p => {
      const playerName = p.playerId?.name || p.name;
      return playerName === currentPlayerName;
    });
    
    // Find the winner (player with highest MMR gain or positive MMR change)
    let winner = null;
    let maxMmrGain = -Infinity;
    
    allPlayers.forEach(p => {
      const mmrChange = p.mmrChange || 0;
      if (mmrChange > maxMmrGain) {
        maxMmrGain = mmrChange;
        winner = p;
      }
    });
    
    // If no clear winner from MMR, use the outcome
    if (!winner && outcome === 'win') {
      winner = currentPlayer;
    }
    
    const playersList = allPlayers.map(p => {
      const playerName = p.playerId?.name || p.name || 'Unknown';
      const isCurrentPlayer = playerName === currentPlayerName;
      const isWinner = winner && (p.playerId?.name || p.name) === (winner.playerId?.name || winner.name);
      
      return `
        <div class="player-detail ${isCurrentPlayer ? 'current-player' : ''} ${isWinner ? 'winner' : 'loser'}">
          <div class="player-detail-header">
            <i class="fas fa-user"></i>
            <span class="player-name">${playerName}</span>
            ${isCurrentPlayer ? '<i class="fas fa-star" title="You"></i>' : ''}
            ${isWinner ? '<span class="result-badge winner">Winner</span>' : '<span class="result-badge loser">Loser</span>'}
          </div>
          <div class="player-detail-info">
            <span class="player-race">${p.race || 'Unknown'} Race</span>
            ${p.mmrChange !== null && p.mmrChange !== undefined ? `<span class="player-mmr">MMR: ${p.mmrChange > 0 ? '+' : ''}${p.mmrChange}</span>` : ''}
            ${p.resources ? `<span class="player-resources">Resources: ${p.resources}</span>` : ''}
          </div>
        </div>
      `;
    }).join('');
    
    return `
      <div class="ffa-players-detailed">
        <div class="ffa-header">
          <span class="ffa-label">Free For All Match</span>
          <span class="ffa-description">All players compete individually</span>
        </div>
        <div class="ffa-players-list">
          ${playersList}
        </div>
      </div>
    `;
  }
  
  // Regular team-based match - organize by teams
  const teams = {};
  allPlayers.forEach(p => {
    // Handle both populated playerId (with name) and direct name field
    const playerName = p.playerId?.name || p.name || 'Unknown';
    const team = p.team || 0;
    const isCurrentPlayer = playerName === currentPlayerName;
    
    if (!teams[team]) {
      teams[team] = [];
    }
    
    teams[team].push({
      name: playerName,
      isCurrentPlayer,
      isAI: p.isAI || false,
      race: p.race || 'Unknown',
      resources: p.resources || null,
      stats: p.stats || null,
      mmrBefore: p.mmrBefore || null,
      mmrAfter: p.mmrAfter || null,
      mmrChange: p.mmrChange || null
    });
  });
  
  // Determine winning team
  const currentPlayerTeam = allPlayers.find(p => {
    const playerName = p.playerId?.name || p.name;
    return playerName === currentPlayerName;
  })?.team || 0;
  const currentPlayerWon = outcome === 'win';
  const winningTeam = currentPlayerWon ? currentPlayerTeam : (currentPlayerTeam === 1 ? 2 : 1);
  
  return Object.keys(teams).sort().map(teamNum => {
    const team = teams[teamNum];
    const isWinningTeam = parseInt(teamNum) === winningTeam;
    const teamClass = isWinningTeam ? 'winning-team' : 'losing-team';
    
    const playersList = team.map(player => `
      <div class="player-detail ${player.isCurrentPlayer ? 'current-player' : ''}">
        <div class="player-detail-header">
          <i class="fas fa-user"></i>
          <span class="player-name">${player.name}</span>
          ${player.isCurrentPlayer ? '<i class="fas fa-star" title="You"></i>' : ''}
          ${isWinningTeam ? '<span class="result-badge winner">Winner</span>' : '<span class="result-badge loser">Loser</span>'}
        </div>
        <div class="player-detail-info">
          <span class="player-race">${player.race} Race</span>
          ${player.mmrChange !== null ? `<span class="player-mmr">MMR: ${player.mmrChange > 0 ? '+' : ''}${player.mmrChange}</span>` : ''}
          ${player.resources ? `<span class="player-resources">Resources: ${player.resources}</span>` : ''}
          ${player.stats ? `<span class="player-stats">Stats: ${JSON.stringify(player.stats)}</span>` : ''}
        </div>
      </div>
    `).join('');
    
    return `
      <div class="team-detail ${teamClass}">
        <div class="team-detail-header">
          <span class="team-label">Team ${teamNum}</span>
          ${isWinningTeam ? '<span class="team-result winner">Winner</span>' : '<span class="team-result loser">Loser</span>'}
        </div>
        <div class="team-players-detailed">
          ${playersList}
        </div>
      </div>
    `;
  }).join('<div class="team-vs-separator-detailed">VS</div>');
}

/**
 * Generate match statistics display
 */
function generateMatchStats(match) {
  const stats = match.stats || {};
  const resources = match.resources || {};
  
  let statsHtml = '';
  
  // Add resource statistics if available
  if (Object.keys(resources).length > 0) {
    statsHtml += `
      <div class="stats-section">
        <h5>Resources</h5>
        <div class="stats-grid">
          ${Object.entries(resources).map(([key, value]) => `
            <div class="stat-item">
              <span class="stat-label">${key}:</span>
              <span class="stat-value">${value}</span>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }
  
  // Add general statistics if available
  if (Object.keys(stats).length > 0) {
    statsHtml += `
      <div class="stats-section">
        <h5>Statistics</h5>
        <div class="stats-grid">
          ${Object.entries(stats).map(([key, value]) => `
            <div class="stat-item">
              <span class="stat-label">${key}:</span>
              <span class="stat-value">${value}</span>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }
  
  return statsHtml || '<p class="no-stats">No detailed statistics available for this match.</p>';
}

/**
 * Format duration from seconds to readable format
 */
function formatDuration(seconds) {
  if (!seconds) return 'Unknown';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  } else {
    return `${secs}s`;
  }
}

/**
 * Toggle match details expansion
 */
window.toggleMatchDetails = function(matchId, event) {
  console.log('🎯 toggleMatchDetails called:', { matchId, event });
  
  // Prevent event bubbling for player links
  if (event.target.closest('.player-link')) {
    console.log('🎯 Clicked on player link, ignoring toggle');
    return;
  }
  
  // Find the match item to get the match data
  const matchItem = event.currentTarget;
  const matchData = matchItem.dataset;
  
  // Get the match details from the expanded content if it exists
  const expandedContent = matchItem.querySelector('.match-details-expanded');
  let matchDetailsHTML = '';
  
  if (expandedContent) {
    // Use the existing expanded content HTML
    matchDetailsHTML = expandedContent.querySelector('.match-details-content').innerHTML;
  } else {
    // Fallback: create basic match info
    const matchHeader = matchItem.querySelector('.match-header');
    const matchDetails = matchItem.querySelector('.match-details');
    
    matchDetailsHTML = `
      <div class="match-details-section">
        <h4><i class="fas fa-info-circle"></i> Match Information</h4>
        <div class="match-info-grid">
          <div class="match-info-item">
            <span class="info-label">Match ID:</span>
            <span class="info-value">${matchId}</span>
          </div>
          <div class="match-info-item">
            <span class="info-label">Status:</span>
            <span class="info-value">Details not available</span>
          </div>
        </div>
      </div>
    `;
  }
  
  // Create and show the match details modal
  showMatchDetailsModal(matchId, matchDetailsHTML);
};

// New function to show match details in a separate modal
window.showMatchDetailsModal = function(matchId, matchDetailsHTML) {
  console.log('🎯 Opening match details modal for:', matchId);
  
  // Create modal HTML
  const modalHTML = `
    <div class="modal" id="match-details-modal">
      <div class="modal-content match-details-modal-content">
        <div class="modal-header">
          <h2><i class="fas fa-gamepad"></i> Match Details</h2>
          <span class="close-modal" onclick="closeMatchDetailsModal()">&times;</span>
        </div>
        <div class="modal-body">
          <div class="match-details-container">
            ${matchDetailsHTML || `
              <div class="no-match-details">
                <i class="fas fa-info-circle"></i>
                <h4>Match Information</h4>
                <p>Detailed match information is not available for this match.</p>
                <div class="match-info-grid">
                  <div class="match-info-item">
                    <span class="info-label">Match ID:</span>
                    <span class="info-value">${matchId}</span>
                  </div>
                </div>
              </div>
            `}
          </div>
        </div>
      </div>
    </div>
  `;
  
  // Remove any existing match details modal
  const existingModal = document.getElementById('match-details-modal');
  if (existingModal) {
    existingModal.remove();
  }
  
  // Add modal to page
  document.body.insertAdjacentHTML('beforeend', modalHTML);
  
  // Show the modal
  const modal = document.getElementById('match-details-modal');
  modal.style.display = 'flex';
  
  // Add fade-in animation
  setTimeout(() => {
    modal.classList.add('show');
  }, 10);
  
  // Close modal when clicking outside
  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      closeMatchDetailsModal();
    }
  });
  
  // Close modal with Escape key
  const escapeHandler = (e) => {
    if (e.key === 'Escape') {
      closeMatchDetailsModal();
      document.removeEventListener('keydown', escapeHandler);
    }
  };
  document.addEventListener('keydown', escapeHandler);
};

// Function to close the match details modal
window.closeMatchDetailsModal = function() {
  const modal = document.getElementById('match-details-modal');
  if (modal) {
    modal.classList.remove('show');
    setTimeout(() => {
      modal.remove();
    }, 300);
  }
};

/**
 * Load performance tab data
 */
async function loadPerformanceData(playerName, contentElement) {
  try {
    // Use the correct API endpoint that exists
    const response = await fetch(`/api/ladder/player/${encodeURIComponent(playerName)}`);

    if (!response.ok) {
      throw new Error('Failed to load player data');
    }

    const data = await response.json();
    const player = data.player || {};
    const stats = player.stats || {};
    
    // Check if this is a WC1 player for special handling
    const isWC1 = player.gameType === 'war1' || player.gameType === 'warcraft1';
    const isWC3 = player.gameType === 'war3' || player.gameType === 'warcraft3';
    
    // Get race performance data
    const raceStats = stats.races || {};
    const raceWins = stats.raceWins || {};
    
    // Create race performance chart
    let raceChartHTML = '';
    if (Object.keys(raceStats).length > 0) {
      raceChartHTML = `
        <div class="performance-section">
          <h4><i class="fas fa-chart-bar"></i> Win Rate by Race</h4>
          <div class="race-chart-container">
            <div class="race-chart">
      `;
      
      Object.entries(raceStats).forEach(([race, totalGames]) => {
        const wins = raceWins[race] || 0;
        const winRate = totalGames > 0 ? Math.round((wins / totalGames) * 100) : 0;
        
        // Get race display info
        let raceDisplay = race.charAt(0).toUpperCase() + race.slice(1);
        let raceIcon = '🎮';
        
        if (isWC1 || player.gameType === 'war2' || player.gameType === 'warcraft2') {
          switch (race) {
            case 'human':
              raceDisplay = 'Human';
              raceIcon = '👤';
              break;
            case 'orc':
              raceDisplay = 'Orc';
              raceIcon = '🟢';
              break;
            case 'random':
              raceDisplay = 'Random';
              raceIcon = '🎲';
              break;
          }
        } else if (isWC3) {
          switch (race) {
            case 'human':
              raceDisplay = 'Human';
              raceIcon = '👤';
              break;
            case 'orc':
              raceDisplay = 'Orc';
              raceIcon = '🟢';
              break;
            case 'undead':
              raceDisplay = 'Undead';
              raceIcon = '💀';
              break;
            case 'night_elf':
              raceDisplay = 'Night Elf';
              raceIcon = '🌙';
              break;
            case 'random':
              raceDisplay = 'Random';
              raceIcon = '🎲';
              break;
          }
        }
        
        const winRateClass = winRate >= 70 ? 'excellent' : winRate >= 60 ? 'good' : winRate >= 50 ? 'average' : 'poor';
        
        raceChartHTML += `
          <div class="race-chart-item">
            <div class="race-chart-header">
              <span class="race-icon">${raceIcon}</span>
              <span class="race-name">${raceDisplay}</span>
              <span class="race-winrate ${winRateClass}">${winRate}%</span>
            </div>
            <div class="race-chart-bars">
              <div class="race-wins-bar" style="width: ${winRate}%"></div>
              <div class="race-losses-bar" style="width: ${100 - winRate}%"></div>
            </div>
            <div class="race-total">${wins}W / ${totalGames - wins}L (${totalGames} total)</div>
          </div>
        `;
      });
      
      raceChartHTML += `
            </div>
          </div>
        </div>
      `;
    }
    
    // Create top maps section (placeholder for now)
    const topMapsHTML = `
      <div class="game-type-maps-section">
        <h4><i class="fas fa-map"></i> Top Maps</h4>
        <div class="game-type-maps">
          <div class="no-detailed-data">
            <i class="fas fa-info-circle"></i>
            <p>Detailed map statistics will be available soon.</p>
          </div>
        </div>
      </div>
    `;
    
    contentElement.innerHTML = `
      <div class="performance-content">
        ${raceChartHTML}
        ${topMapsHTML}
      </div>
    `;
    
  } catch (error) {
    console.error('Error loading performance data:', error);
    contentElement.innerHTML = `
      <div class="error-message">
        <i class="fas fa-exclamation-triangle"></i>
        Failed to load performance data
      </div>
    `;
  }
}