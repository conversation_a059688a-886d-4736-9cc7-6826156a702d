/* Enhanced Game Manager CSS - Modern Design System */

:root {
  /* Enhanced Color Palette */
  --primary-color: #1a472a;
  --primary-light: #2d5a3d;
  --primary-dark: #0f2e1a;
  --secondary-color: #8B4513;
  --accent-color: #DAA520;
  --accent-light: #F4E4BC;
  
  /* Status Colors */
  --success-color: #22c55e;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
  
  /* Victory/Defeat Colors */
  --victory-color: #ffd700;
  --victory-bg: rgba(255, 215, 0, 0.1);
  --defeat-color: #dc2626;
  --defeat-bg: rgba(220, 38, 38, 0.1);
  
  /* Modern Grays */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Spacing System */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
}

/* Enhanced Game Manager Container */
.enhanced-game-manager {
  max-width: 1400px;
  margin: 0 auto;
  padding: var(--space-lg);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: calc(100vh - 100px);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

/* Enhanced Header */
.gm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xl);
  padding: var(--space-lg);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  border-radius: var(--radius-lg);
  color: white;
  box-shadow: var(--shadow-lg);
}

.status-indicators {
  display: flex;
  gap: var(--space-lg);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  font-size: 0.9rem;
  font-weight: 500;
}

.status-item.active {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-item.inactive {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.stats-summary {
  display: flex;
  gap: var(--space-lg);
}

.stat-box {
  text-align: center;
  padding: var(--space-md);
  background: rgba(255, 255, 255, 0.15);
  border-radius: var(--radius-md);
  backdrop-filter: blur(10px);
  min-width: 80px;
}

.stat-number {
  font-size: 1.75rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: 0.75rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Navigation */
.gm-navigation {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-xl);
  background: white;
  padding: var(--space-sm);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.nav-btn {
  flex: 1;
  padding: var(--space-md) var(--space-lg);
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.95rem;
  color: var(--gray-600);
  transition: var(--transition-normal);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.nav-btn:hover {
  background: var(--gray-100);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.nav-btn.active {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
  box-shadow: var(--shadow-md);
}

.nav-btn.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.dashboard-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--primary-color));
}

.dashboard-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.dashboard-card h3 {
  margin: 0 0 var(--space-lg) 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-800);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

/* Activity Feed */
.activity-list {
  max-height: 300px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--gray-300) transparent;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  margin-bottom: var(--space-sm);
}

.activity-item:hover {
  background: var(--gray-50);
}

.activity-icon {
  font-size: 1.5rem;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--gray-100);
}

.activity-details {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--space-xs);
}

.activity-subtitle {
  font-size: 0.875rem;
  color: var(--gray-500);
}

.activity-confidence {
  background: var(--primary-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

/* Advanced Statistics */
.stats-grid {
  display: grid;
  gap: var(--space-lg);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.stat-item:hover {
  background: var(--gray-100);
  transform: scale(1.02);
}

.stat-label {
  font-weight: 500;
  color: var(--gray-700);
}

.stat-value {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--primary-color);
}

.stat-bar {
  width: 100%;
  height: 6px;
  background: var(--gray-200);
  border-radius: 3px;
  overflow: hidden;
  margin-top: var(--space-sm);
}

.stat-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  transition: width var(--transition-normal);
  border-radius: 3px;
}

/* Game Statistics */
.game-stats {
  display: grid;
  gap: var(--space-md);
}

.game-stat-item {
  padding: var(--space-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.game-stat-item:hover {
  background: var(--gray-100);
  transform: translateX(4px);
}

.game-stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.game-stat-name {
  font-weight: 600;
  color: var(--gray-800);
}

.game-stat-winrate {
  background: var(--accent-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

.game-stat-details {
  display: flex;
  gap: var(--space-md);
  font-size: 0.875rem;
}

.victories {
  color: var(--success-color);
  font-weight: 600;
}

.defeats {
  color: var(--error-color);
  font-weight: 600;
}

/* Quick Actions */
.action-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--space-md);
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg);
  background: var(--gray-50);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
  cursor: pointer;
  text-decoration: none;
  color: var(--gray-700);
}

.quick-action-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quick-action-btn .icon {
  font-size: 2rem;
}

.quick-action-btn .label {
  font-weight: 600;
  font-size: 0.875rem;
}

/* Screenshots View */
.screenshots-view {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  box-shadow: var(--shadow-md);
}

.screenshots-filters {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
  padding: var(--space-lg);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  min-width: 150px;
}

.filter-group label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
}

.filter-group select,
.filter-group input {
  padding: var(--space-sm) var(--space-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  background: white;
  transition: var(--transition-fast);
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(26, 71, 42, 0.1);
}

/* Screenshots Grid */
.screenshots-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

/* Enhanced Screenshot Cards */
.screenshot-card {
  background: white;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: var(--transition-normal);
  border: 2px solid transparent;
  position: relative;
}

.screenshot-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.screenshot-card.victory {
  border-color: var(--victory-color);
}

.screenshot-card.defeat {
  border-color: var(--defeat-color);
}

.screenshot-preview {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.screenshot-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition-normal);
}

.screenshot-card:hover .screenshot-preview img {
  transform: scale(1.05);
}

.screenshot-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.7) 100%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: var(--space-md);
}

.result-badge {
  align-self: flex-start;
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-weight: 700;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.result-badge.victory {
  background: var(--victory-color);
  color: black;
}

.result-badge.defeat {
  background: var(--defeat-color);
  color: white;
}

.confidence-badge {
  align-self: flex-end;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

/* Screenshot Info */
.screenshot-info {
  padding: var(--space-lg);
}

.info-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.game-type {
  background: var(--primary-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.timestamp {
  font-size: 0.875rem;
  color: var(--gray-500);
}

.players-info,
.map-info,
.duration-info,
.analysis-methods,
.achievements {
  margin-bottom: var(--space-md);
  font-size: 0.875rem;
}

.players-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-top: var(--space-xs);
}

.player-tag {
  background: var(--accent-light);
  color: var(--secondary-color);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

.methods-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-top: var(--space-xs);
}

.method-tag {
  background: var(--info-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.achievement-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-top: var(--space-xs);
}

.achievement-badge {
  background: var(--accent-color);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 600;
}

/* Screenshot Actions */
.screenshot-actions {
  display: flex;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
}

/* Enhanced Action Buttons */
.action-btn {
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: var(--space-xs);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-normal);
}

.action-btn:hover::before {
  left: 100%;
}

.action-btn.primary {
  background: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.action-btn.secondary {
  background: var(--gray-200);
  color: var(--gray-700);
}

.action-btn.secondary:hover {
  background: var(--gray-300);
  transform: translateY(-1px);
}

.action-btn.tertiary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.action-btn.tertiary:hover {
  background: var(--primary-color);
  color: white;
}

.action-btn.small {
  padding: var(--space-xs) var(--space-sm);
  font-size: 0.75rem;
}

.action-btn.large {
  padding: var(--space-md) var(--space-xl);
  font-size: 1rem;
}

.action-btn:disabled,
.action-btn.reported {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--gray-300);
  color: var(--gray-500);
}

.action-btn:disabled:hover,
.action-btn.reported:hover {
  transform: none;
  box-shadow: none;
}

/* No Screenshots State */
.no-screenshots {
  text-align: center;
  padding: var(--space-2xl);
  color: var(--gray-500);
}

.no-screenshots-icon {
  font-size: 4rem;
  margin-bottom: var(--space-lg);
  opacity: 0.5;
}

.no-screenshots-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: var(--space-md);
  color: var(--gray-700);
}

.no-screenshots-subtitle {
  font-size: 1rem;
  margin-bottom: var(--space-xl);
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Settings View */
.settings-view {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  max-width: 800px;
  margin: 0 auto;
}

.settings-form {
  display: grid;
  gap: var(--space-xl);
}

.settings-section {
  padding: var(--space-lg);
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.settings-section h3 {
  margin: 0 0 var(--space-lg) 0;
  color: var(--gray-800);
  font-size: 1.25rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.setting-item {
  margin-bottom: var(--space-lg);
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-label {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  font-weight: 600;
  color: var(--gray-700);
  cursor: pointer;
  position: relative;
}

.setting-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  background: white;
  position: relative;
  transition: var(--transition-fast);
}

.setting-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.setting-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-weight: bold;
  font-size: 12px;
}

.setting-description {
  margin: var(--space-sm) 0 0 0;
  font-size: 0.875rem;
  color: var(--gray-500);
  line-height: 1.5;
}

.slider-container {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-top: var(--space-sm);
}

.setting-slider {
  flex: 1;
  height: 6px;
  background: var(--gray-200);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.setting-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.setting-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

.slider-value {
  min-width: 60px;
  text-align: center;
  font-weight: 600;
  color: var(--primary-color);
  background: var(--gray-100);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
}

/* Game Types Grid */
.game-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-top: var(--space-md);
}

.game-type-label {
  cursor: pointer;
}

.game-type-label input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  pointer-events: none;
}

.game-type-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg);
  background: white;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-lg);
  transition: var(--transition-normal);
}

.game-type-label input[type="checkbox"]:checked + .game-type-card {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  transform: scale(1.02);
}

.game-type-card:hover {
  border-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.game-icon {
  font-size: 2rem;
}

.game-name {
  font-weight: 600;
  font-size: 0.875rem;
}

.settings-actions {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  padding-top: var(--space-lg);
  border-top: 1px solid var(--gray-200);
}

/* History View */
.history-view {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
}

.history-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.stat-card {
  background: var(--gray-50);
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
  text-align: center;
  border: 1px solid var(--gray-200);
  transition: var(--transition-normal);
}

.stat-card:hover {
  background: var(--gray-100);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-card h4 {
  margin: 0 0 var(--space-md) 0;
  color: var(--gray-600);
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

/* Timeline */
.history-timeline {
  margin-bottom: var(--space-xl);
}

.timeline-container {
  position: relative;
  padding-left: var(--space-xl);
}

.timeline-container::before {
  content: '';
  position: absolute;
  left: var(--space-md);
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--gray-200);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--space-lg);
  padding: var(--space-md);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: var(--transition-normal);
}

.timeline-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateX(4px);
}

.timeline-item.victory {
  border-left: 4px solid var(--victory-color);
}

.timeline-item.defeat {
  border-left: 4px solid var(--defeat-color);
}

.timeline-marker {
  position: absolute;
  left: calc(-1 * var(--space-xl) - 6px);
  top: var(--space-md);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--primary-color);
  border: 2px solid white;
  box-shadow: var(--shadow-sm);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-sm);
}

.timeline-result {
  font-weight: 700;
  color: var(--gray-800);
}

.timeline-time {
  font-size: 0.875rem;
  color: var(--gray-500);
}

.timeline-details {
  font-size: 0.875rem;
  color: var(--gray-600);
}

.timeline-game {
  font-weight: 600;
}

.timeline-map,
.timeline-confidence {
  color: var(--gray-500);
}

/* Manual Report Form */
.manual-report-section {
  background: var(--gray-50);
  padding: var(--space-xl);
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
}

.manual-report-form {
  max-width: 600px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-group label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 0.875rem;
}

.form-group input,
.form-group select {
  padding: var(--space-md);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  background: white;
  transition: var(--transition-fast);
  font-size: 0.875rem;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(26, 71, 42, 0.1);
}

/* Notifications */
.notification {
  position: fixed;
  top: var(--space-lg);
  right: var(--space-lg);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  font-weight: 600;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
  max-width: 400px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification-success {
  background: var(--success-color);
  color: white;
}

.notification-error {
  background: var(--error-color);
  color: white;
}

.notification-warning {
  background: var(--warning-color);
  color: white;
}

.notification-info {
  background: var(--info-color);
  color: white;
}

/* Action Bar */
.gm-actions {
  display: flex;
  justify-content: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

/* Game Manager Dropdown Styles */
.game-manager-main .nav-dropdown {
  position: relative;
}

.game-manager-main .nav-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  min-width: 200px;
  background: var(--bg-dropdown);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: var(--z-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
}

.game-manager-main .nav-dropdown.active .nav-dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.game-manager-main .nav-dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  color: var(--text-color);
  text-decoration: none;
  transition: background 0.2s ease;
}

.game-manager-main .nav-dropdown-item:hover {
  background: var(--bg-hover);
}

.game-manager-main .nav-dropdown-item i {
  margin-right: 10px;
  width: 16px;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-game-manager {
    padding: var(--space-md);
  }
  
  .gm-header {
    flex-direction: column;
    gap: var(--space-lg);
  }
  
  .status-indicators {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .stats-summary {
    justify-content: center;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .screenshots-grid {
    grid-template-columns: 1fr;
  }
  
  .screenshots-filters {
    flex-direction: column;
    gap: var(--space-md);
  }
  
  .action-grid {
    grid-template-columns: 1fr;
  }
  
  .game-types-grid {
    grid-template-columns: 1fr;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .gm-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .nav-btn {
    font-size: 0.8rem;
    padding: var(--space-sm) var(--space-md);
  }
  
  .stat-box {
    min-width: 60px;
  }
  
  .stat-number {
    font-size: 1.25rem;
  }
  
  .history-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-400: #9ca3af;
    --gray-500: #d1d5db;
    --gray-600: #e5e7eb;
    --gray-700: #f3f4f6;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
  }
  
  .enhanced-game-manager {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  }
} 

/* Download Prompt Styles */
.download-prompt {
  text-align: center;
  padding: 40px;
  background: var(--bg-card);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 40px auto;
  max-width: 600px;
}

.download-prompt h2 {
  font-size: 24px;
  margin-bottom: 16px;
  color: var(--text-primary);
}

.download-prompt p {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 24px;
  line-height: 1.5;
}

.download-prompt .btn-primary {
  display: inline-block;
  padding: 12px 24px;
  background: var(--primary-color);
  color: white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: background 0.2s ease;
}

.download-prompt .btn-primary:hover {
  background: var(--primary-color-hover);
} 
