<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WC Arena - Player Profile</title>
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/profile.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    /* Game Type Section Styles */
    .game-type-section {
      background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
      border: 2px solid #ffd700;
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    }

    .game-type-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 2px solid #333;
    }

    .game-type-title {
      display: flex;
      align-items: center;
      gap: 1rem;
      color: #ffd700;
      font-size: 2rem;
      font-weight: bold;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .game-type-icon {
      font-size: 2.5rem;
    }

    .game-type-status {
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-weight: bold;
      font-size: 0.9rem;
    }

    .status-active {
      background: rgba(76, 175, 80, 0.2);
      color: #4CAF50;
      border: 1px solid #4CAF50;
    }

    .status-inactive {
      background: rgba(158, 158, 158, 0.2);
      color: #9e9e9e;
      border: 1px solid #9e9e9e;
    }

    .no-players-message {
      text-align: center;
      padding: 3rem;
      color: #999;
      font-style: italic;
    }

    .no-players-message i {
      font-size: 3rem;
      margin-bottom: 1rem;
      display: block;
      opacity: 0.5;
    }

    .create-player-btn {
      background: linear-gradient(135deg, #8b4513 0%, #cd853f 100%);
      color: white;
      border: none;
      padding: 0.8rem 1.5rem;
      border-radius: 6px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      margin-top: 1rem;
    }

    .create-player-btn:hover {
      background: linear-gradient(135deg, #cd853f 0%, #daa520 100%);
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(139, 69, 19, 0.4);
    }

    .player-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-top: 1rem;
    }

    .player-card {
      background: rgba(0, 0, 0, 0.3);
      border: 1px solid #444;
      border-radius: 8px;
      padding: 1.5rem;
      transition: all 0.3s ease;
    }

    .player-card:hover {
      border-color: #ffd700;
      box-shadow: 0 5px 15px rgba(255, 215, 0, 0.2);
      transform: translateY(-3px);
    }

    .player-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .player-name {
      color: #ffd700;
      font-size: 1.3rem;
      font-weight: bold;
    }

    .player-race {
      background: rgba(255, 255, 255, 0.1);
      padding: 0.3rem 0.8rem;
      border-radius: 15px;
      font-size: 0.8rem;
      text-transform: capitalize;
    }

    .player-stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      display: block;
      font-size: 1.5rem;
      font-weight: bold;
      color: #ffd700;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #ccc;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .player-rank {
      text-align: center;
      margin-bottom: 1rem;
    }

    .rank-image {
      width: 60px;
      height: 60px;
      margin: 0 auto 0.5rem;
      display: block;
    }

    .rank-name {
      color: #ffd700;
      font-weight: bold;
      margin-bottom: 0.3rem;
    }

    .mmr-value {
      color: #fff;
      font-size: 1.1rem;
    }

    /* War2 Specific Styling */
    .war2-section {
      border-color: #ff4500;
    }

    .war2-section .game-type-title {
      color: #ff4500;
    }

    .war2-section .player-name {
      color: #ff4500;
    }

    .war2-section .stat-value {
      color: #ff4500;
    }

    /* War3 Specific Styling */
    .war3-section {
      border-color: #8a2be2;
    }

    .war3-section .game-type-title {
      color: #8a2be2;
    }

    .war3-section .player-name {
      color: #8a2be2;
    }

    .war3-section .stat-value {
      color: #8a2be2;
    }

    /* Race specific colors */
    .race-human { background-color: rgba(65, 105, 225, 0.3); }
    .race-orc { background-color: rgba(220, 20, 60, 0.3); }
    .race-undead { background-color: rgba(75, 0, 130, 0.3); }
    .race-night_elf { background-color: rgba(0, 128, 0, 0.3); }
    .race-random { background-color: rgba(255, 165, 0, 0.3); }

    @media (max-width: 768px) {
      .game-type-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
      }

      .player-grid {
        grid-template-columns: 1fr;
      }

      .player-stats {
        grid-template-columns: 1fr;
      }
    }
  </style>
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->`r`n  <link rel=stylesheet href=/css/navbar-universal.css />`r`n</head>
<body>
  <div id="navbar-container"></div>

  <main class="profile-main">
    <div class="profile-container">
      <div class="profile-header">
        <div class="profile-avatar-container" id="profile-avatar">
          <img src="/assets/img/ranks/emblem.png" alt="Player Avatar" id="player-avatar">
        </div>
        <div class="profile-info">
          <h1 class="profile-name" id="profile-name">Loading...</h1>
          <div class="profile-meta">
            <span class="join-date" id="join-date">Member since: Loading...</span>
            <span class="total-matches" id="total-matches">Total Matches: 0</span>
          </div>
        </div>
      </div>

      <!-- WC II Section -->
      <div class="game-type-section war2-section">
        <div class="game-type-header">
          <div class="game-type-title">
            <span class="game-type-icon">⚔️</span>
            <span>WC II Ladder</span>
          </div>
          <div class="game-type-status" id="war2-status">
            <span class="status-inactive">Not Active</span>
          </div>
        </div>

        <div id="war2-content">
          <div class="no-players-message">
            <i class="fas fa-shield-alt"></i>
            <p>No WC II characters created yet.</p>
            <p>Create your first character to join the War2 ladder!</p>
            <button class="create-player-btn" onclick="createWar2Player()">
              <i class="fas fa-plus"></i> Create War2 Character
            </button>
          </div>
        </div>

        <div id="war2-players" class="player-grid" style="display: none;">
          <!-- War2 players will be loaded here -->
        </div>
      </div>

      <!-- WC III Section -->
      <div class="game-type-section war3-section">
        <div class="game-type-header">
          <div class="game-type-title">
            <span class="game-type-icon">🏰</span>
            <span>WC III Ladder</span>
          </div>
          <div class="game-type-status" id="war3-status">
            <span class="status-inactive">Not Active</span>
          </div>
        </div>

        <div id="war3-content">
          <div class="no-players-message">
            <i class="fas fa-dragon"></i>
            <p>No WC III characters created yet.</p>
            <p>Create your first character to join the War3 ladder!</p>
            <button class="create-player-btn" onclick="createWar3Player()">
              <i class="fas fa-plus"></i> Create War3 Character
            </button>
          </div>
        </div>

        <div id="war3-players" class="player-grid" style="display: none;">
          <!-- War3 players will be loaded here -->
        </div>
      </div>

      <!-- Match History Section -->
      <div class="game-type-section">
        <div class="game-type-header">
          <div class="game-type-title">
            <span class="game-type-icon">📋</span>
            <span>Recent Match History</span>
          </div>
        </div>

        <div class="match-history-filters">
          <select id="game-filter">
            <option value="all">All Games</option>
            <option value="warcraft1">WC I Only</option>
            <option value="war2">WC II Only</option>
            <option value="war3">WC III Only</option>
          </select>
          <select id="result-filter">
            <option value="all">All Results</option>
            <option value="win">Wins Only</option>
            <option value="loss">Losses Only</option>
          </select>
        </div>

        <div id="match-history" class="match-history-container">
          <div class="loading">Loading match history...</div>
        </div>
      </div>
    </div>
  </main>

  <div id="footer-container"></div>

  <script src="/js/utils.js"></script>
      <script type="module" src="/js/modules/ApiClient.js"></script>
  <script src="/js/modules/GameTypeManager.js"></script>
  <script src="/js/main.js"></script>
  <script>
    let apiClient;
    let gameTypeManager;
    let currentUser;

    document.addEventListener('DOMContentLoaded', async () => {
      apiClient = new ApiClient();
      gameTypeManager = new GameTypeManager();
      
      await loadProfile();
    });

    async function loadProfile() {
      try {
        // Load current user
        const userResponse = await apiClient.get('/api/me');
        if (!userResponse.success) {
          throw new Error('Failed to load user data');
        }
        currentUser = userResponse.data;

        // Update profile header
        document.getElementById('profile-name').textContent = currentUser.username;
        document.getElementById('join-date').textContent = `Member since: ${new Date(currentUser.createdAt).toLocaleDateString()}`;

        // Load players data
        const playersResponse = await apiClient.get('/api/ladder/my-players');
        if (playersResponse.success) {
          const players = playersResponse.data;
          
          // Separate players by game type
          const war2Players = players.filter(p => p.gameType === 'war2' || !p.gameType); // Default to war2 for legacy
          const war3Players = players.filter(p => p.gameType === 'war3');

          // Update sections
          updateWar2Section(war2Players);
          updateWar3Section(war3Players);

          // Update total matches count
          const totalMatches = players.reduce((total, player) => total + (player.wins || 0) + (player.losses || 0), 0);
          document.getElementById('total-matches').textContent = `Total Matches: ${totalMatches}`;
        }

        // Load match history
        await loadMatchHistory();

      } catch (error) {
        console.error('Error loading profile:', error);
        showError('Failed to load profile data');
      }
    }

    function updateWar2Section(players) {
      const statusEl = document.getElementById('war2-status');
      const contentEl = document.getElementById('war2-content');
      const playersEl = document.getElementById('war2-players');

      if (players.length > 0) {
        statusEl.innerHTML = '<span class="status-active">Active</span>';
        contentEl.style.display = 'none';
        playersEl.style.display = 'grid';
        playersEl.innerHTML = players.map(player => createPlayerCard(player, 'war2')).join('');
      } else {
        statusEl.innerHTML = '<span class="status-inactive">Not Active</span>';
        contentEl.style.display = 'block';
        playersEl.style.display = 'none';
      }
    }

    function updateWar3Section(players) {
      const statusEl = document.getElementById('war3-status');
      const contentEl = document.getElementById('war3-content');
      const playersEl = document.getElementById('war3-players');

      if (players.length > 0) {
        statusEl.innerHTML = '<span class="status-active">Active</span>';
        contentEl.style.display = 'none';
        playersEl.style.display = 'grid';
        playersEl.innerHTML = players.map(player => createPlayerCard(player, 'war3')).join('');
      } else {
        statusEl.innerHTML = '<span class="status-inactive">Not Active</span>';
        contentEl.style.display = 'block';
        playersEl.style.display = 'none';
      }
    }

    function createPlayerCard(player, gameType) {
      const winRate = player.totalMatches > 0 ? 
        Math.round((player.wins / player.totalMatches) * 100) : 0;
      
      const rankImage = player.rank ? 
        `/assets/img/ranks/${player.rank}.png` : 
        '/assets/img/ranks/emblem.png';

      return `
        <div class="player-card">
          <div class="player-header">
            <div class="player-name">${player.name}</div>
            <div class="player-race race-${player.race}">${player.race}</div>
          </div>
          
          <div class="player-rank">
            <img src="${rankImage}" alt="${player.rank || 'Unranked'}" class="rank-image" 
                 onerror="this.src='/assets/img/ranks/emblem.png'">
            <div class="rank-name">${player.rank || 'Unranked'}</div>
            <div class="mmr-value">${player.mmr || 1000} MMR</div>
          </div>

          <div class="player-stats">
            <div class="stat-item">
              <span class="stat-value">${player.wins || 0}</span>
              <span class="stat-label">Wins</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">${player.losses || 0}</span>
              <span class="stat-label">Losses</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">${winRate}%</span>
              <span class="stat-label">Win Rate</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">${player.totalMatches || 0}</span>
              <span class="stat-label">Total</span>
            </div>
          </div>
        </div>
      `;
    }

    async function loadMatchHistory() {
      try {
        const response = await apiClient.get('/api/ladder/matches?limit=20');
        if (response.success) {
          displayMatchHistory(response.data);
        }
      } catch (error) {
        console.error('Error loading match history:', error);
        document.getElementById('match-history').innerHTML = 
          '<div class="error">Failed to load match history</div>';
      }
    }

    function displayMatchHistory(matches) {
      const container = document.getElementById('match-history');
      
      if (!matches || matches.length === 0) {
        container.innerHTML = '<div class="no-matches">No matches found</div>';
        return;
      }

      container.innerHTML = matches.map(match => {
        const gameTypeIcon = match.gameType === 'war3' ? '🏰' : '⚔️';
        const gameTypeName = match.gameType === 'war3' ? 'War3' : 'War2';
        const date = new Date(match.createdAt).toLocaleDateString();
        
        return `
          <div class="match-item">
            <div class="match-header">
              <span class="game-type-badge">${gameTypeIcon} ${gameTypeName}</span>
              <span class="match-type">${match.matchType}</span>
              <span class="match-date">${date}</span>
            </div>
            <div class="match-details">
              <span class="match-map">${match.mapName || 'Unknown Map'}</span>
              <span class="match-result ${match.result}">${match.result}</span>
            </div>
          </div>
        `;
      }).join('');
    }

    function createWar2Player() {
      // Set game type and redirect to player creation
      if (gameTypeManager) {
        gameTypeManager.currentGameType = 'war2';
      }
      window.location.href = '/ladder?action=create-player&gameType=war2';
    }

    function createWar3Player() {
      // Set game type and redirect to player creation  
      if (gameTypeManager) {
        gameTypeManager.currentGameType = 'war3';
      }
      window.location.href = '/ladder?action=create-player&gameType=war3';
    }

    function showError(message) {
      const errorDiv = document.createElement('div');
      errorDiv.className = 'error-message';
      errorDiv.textContent = message;
      errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #f44336;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 10000;
      `;
      
      document.body.appendChild(errorDiv);
      
      setTimeout(() => {
        if (errorDiv.parentNode) {
          errorDiv.remove();
        }
      }, 5000);
    }

    // Filter functionality
    document.addEventListener('change', (e) => {
      if (e.target.id === 'game-filter' || e.target.id === 'result-filter') {
        loadMatchHistory(); // Reload with filters
      }
    });
  </script>
</body>
</html> 
