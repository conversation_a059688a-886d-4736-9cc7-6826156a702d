<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WC Arena - Next-Gen Forum</title>
  
  <!-- Preload critical resources -->
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-brands-400.woff2" as="font" type="font/woff2" crossorigin>
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/webfonts/fa-solid-900.woff2" as="font" type="font/woff2" crossorigin>
  
  <!-- Core Stylesheets -->
  <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
  <link rel="stylesheet" href="/css/forum-buttons.css" />
  <link rel="stylesheet" href="/css/poll-modal.css" />
  <link rel="stylesheet" href="/css/emoji-picker.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->
  <link rel="stylesheet" href="/css/navbar-universal.css" />
  
  <!-- Socket.IO for live updates -->
  <script src="/socket.io/socket.io.js"></script>
  
  <style>
    /* ===== NEXT-GEN FORUM STYLING ===== */
    
    /* Main Container */
    .forum-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: var(--space-6);
      padding-top: calc(80px + var(--space-6)); /* Add navbar height (80px) plus regular padding */
      min-height: 100vh;
    }

    /* Forum Header */
    .forum-header {
      text-align: center;
      margin-bottom: var(--space-8);
      position: relative;
    }

    .forum-title {
      font-family: var(--font-display);
      font-size: 3.5rem;
      font-weight: 800;
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      margin-bottom: var(--space-3);
      text-shadow: 0 0 40px rgba(212, 175, 55, 0.6);
      letter-spacing: -0.025em;
    }

    .forum-subtitle {
      color: var(--neutral-300);
      font-size: 1.25rem;
      font-weight: 500;
      max-width: 600px;
      margin: 0 auto;
    }

    /* Game Selector Tabs (Like Maps Page) */
    .game-tabs-container {
      margin-bottom: var(--space-8);
      display: flex;
      justify-content: center;
    }

    .game-tabs-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 2rem;
      flex-wrap: wrap;
    }

    .game-tabs {
      display: flex;
      gap: 0.4rem;
      justify-content: center;
      flex: 1;
    }

    .game-tab {
      position: relative;
      display: flex;
      align-items: center;
      gap: 0.3rem;
      padding: 0.4rem 0.8rem;
      background: rgba(255, 255, 255, 0.03);
      border: 1px solid rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      color: rgba(255, 255, 255, 0.7);
      font-family: 'Cinzel', serif;
      font-size: 0.7rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      overflow: hidden;
      min-width: 100px;
      justify-content: center;
    }

    .game-tab::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.2), transparent);
      transition: left 0.6s;
    }

    .game-tab:hover {
      background: rgba(255, 215, 0, 0.08);
      border-color: rgba(255, 215, 0, 0.2);
      color: #ffd700;
      transform: translateY(-1px) scale(1.02);
      box-shadow: 0 3px 10px rgba(255, 215, 0, 0.2);
    }

    .game-tab:hover::before {
      left: 100%;
    }

    .game-tab.active {
      background: linear-gradient(45deg, rgba(255, 215, 0, 0.15), rgba(255, 237, 78, 0.15));
      border-color: #ffd700;
      color: #ffd700;
      transform: translateY(-1px) scale(1.02);
      box-shadow: 0 3px 10px rgba(255, 215, 0, 0.3);
    }

    .game-tab i {
      font-size: 0.9rem;
    }

    .game-tab span {
      font-size: 0.7rem;
    }

    /* Forum Stats */
    .forum-stats {
      display: flex;
      gap: var(--space-4);
      align-items: center;
    }

    .forum-stat {
      text-align: center;
      padding: var(--space-3) var(--space-4);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-lg);
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .forum-stat-value {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--primary-gold);
      margin-bottom: var(--space-1);
    }

    .forum-stat-label {
      font-size: 0.875rem;
      color: var(--neutral-400);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    /* Main Forum Layout */
    .forum-main {
      display: grid;
      grid-template-columns: 1fr 300px;
      gap: var(--space-8);
      align-items: start;
    }

    @media (max-width: 1024px) {
      .forum-main {
        grid-template-columns: 1fr;
        gap: var(--space-6);
      }
    }

    /* Post Composer */
    .post-composer {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-2xl);
      padding: var(--space-6);
      margin-bottom: var(--space-6);
      box-shadow: var(--glass-shadow);
    }

    .composer-header {
      display: flex;
      align-items: center;
      gap: var(--space-4);
      margin-bottom: var(--space-4);
    }

    .composer-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      border: 2px solid var(--primary-gold);
      object-fit: cover;
    }

    .composer-input-area {
      flex: 1;
    }

    .composer-textarea {
      width: 100%;
      min-height: 120px;
      background: var(--bg-card);
      border: 2px solid var(--glass-border);
      border-radius: var(--radius-xl);
      padding: var(--space-4);
      color: var(--neutral-100);
      font-family: var(--font-primary);
      font-size: 1rem;
      line-height: 1.6;
      resize: vertical;
      transition: all var(--transition-normal);
    }

    .composer-textarea:focus {
      outline: none;
      border-color: var(--primary-gold);
      box-shadow: 0 0 0 4px rgba(212, 175, 55, 0.2);
      background: var(--bg-card-hover);
    }

    .composer-textarea::placeholder {
      color: var(--neutral-500);
      font-style: italic;
    }

    .composer-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: var(--space-4);
      padding-top: var(--space-4);
      border-top: 1px solid var(--glass-border);
    }

    .composer-tools {
      display: flex;
      gap: var(--space-2);
    }

    .composer-tool {
      width: 40px;
      height: 40px;
      border: none;
      background: var(--bg-card);
      color: var(--neutral-400);
      border-radius: var(--radius-lg);
      cursor: pointer;
      transition: all var(--transition-normal);
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid var(--glass-border);
    }

    .composer-tool:hover {
      background: var(--bg-card-hover);
      color: var(--primary-gold);
      border-color: var(--primary-gold);
      transform: translateY(-1px);
    }

    .post-button {
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      color: var(--neutral-900);
      border: none;
      padding: var(--space-3) var(--space-6);
      border-radius: var(--radius-xl);
      font-family: var(--font-display);
      font-weight: 600;
      font-size: 1rem;
      cursor: pointer;
      transition: all var(--transition-normal);
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 4px 16px rgba(212, 175, 55, 0.3);
    }

    .post-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 32px rgba(212, 175, 55, 0.5);
    }

    .post-button:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    /* Live Posts Feed */
    .posts-feed {
      space-y: var(--space-6);
    }

    .post-card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-2xl);
      padding: var(--space-6);
      margin-bottom: var(--space-6);
      box-shadow: var(--glass-shadow);
      transition: all var(--transition-normal);
      position: relative;
      overflow: hidden;
    }

    .post-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, transparent, var(--primary-gold), transparent);
      opacity: 0;
      transition: var(--transition-normal);
    }

    .post-card:hover::before {
      opacity: 1;
    }

    .post-card:hover {
      transform: translateY(-2px);
      border-color: rgba(212, 175, 55, 0.3);
      box-shadow: 0 12px 48px rgba(0, 0, 0, 0.5);
    }

    .post-header {
      display: flex;
      align-items: center;
      gap: var(--space-4);
      margin-bottom: var(--space-4);
    }

    .post-avatar {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      border: 3px solid var(--primary-gold);
      object-fit: cover;
      box-shadow: 0 4px 16px rgba(212, 175, 55, 0.3);
    }

    .post-author-info {
      flex: 1;
    }

    .post-author-name {
      font-family: var(--font-display);
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--neutral-100);
      margin-bottom: var(--space-1);
    }

    .post-meta {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      color: var(--neutral-400);
      font-size: 0.875rem;
    }

    .post-game-tag {
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      color: var(--neutral-900);
      padding: var(--space-1) var(--space-3);
      border-radius: var(--radius-md);
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .post-time {
      position: relative;
    }

    .post-live-indicator {
      display: inline-flex;
      align-items: center;
      gap: var(--space-1);
      color: #10b981;
      font-size: 0.75rem;
      font-weight: 500;
    }

    .post-live-dot {
      width: 6px;
      height: 6px;
      background: #10b981;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .post-content {
      color: var(--neutral-200);
      line-height: 1.7;
      font-size: 1rem;
      margin-bottom: var(--space-5);
    }

    .post-content p {
      margin-bottom: var(--space-3);
    }

    .post-content img {
      max-width: 100%;
      height: auto;
      border-radius: var(--radius-lg);
      margin: var(--space-3) 0;
    }

    /* Poll Styles */
    .poll-container {
      background: linear-gradient(135deg, 
        rgba(212, 175, 55, 0.1) 0%, 
        rgba(212, 175, 55, 0.05) 100%);
      border: 2px solid rgba(212, 175, 55, 0.3);
      border-radius: var(--radius-xl);
      padding: var(--space-6);
      margin: var(--space-6) 0;
      box-shadow: 0 8px 32px rgba(212, 175, 55, 0.15);
    }

    .poll-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--space-5);
    }

    .poll-question {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--primary-gold);
      display: flex;
      align-items: center;
      gap: var(--space-3);
    }

    .poll-question i {
      font-size: 1.4rem;
      opacity: 0.8;
    }

    .poll-expiration {
      font-size: 0.9rem;
      color: var(--neutral-400);
      display: flex;
      align-items: center;
      gap: var(--space-2);
    }

    .poll-options {
      margin-bottom: var(--space-5);
    }

    .poll-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--space-4);
      margin-bottom: var(--space-3);
      background: rgba(0, 0, 0, 0.3);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: var(--radius-lg);
      transition: all var(--transition-normal);
      position: relative;
      overflow: hidden;
    }

    .poll-option:hover {
      background: rgba(0, 0, 0, 0.4);
      border-color: rgba(212, 175, 55, 0.3);
    }

    .poll-option.user-vote {
      border-color: var(--primary-gold);
      box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
    }

    .poll-option-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      z-index: 2;
      position: relative;
    }

    .poll-option-text {
      font-size: 1.1rem;
      font-weight: 500;
      color: var(--neutral-100);
    }

    .poll-option-votes {
      font-size: 0.9rem;
      color: var(--neutral-300);
      font-weight: 600;
    }

    .poll-option-bar {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 100%;
      background: rgba(212, 175, 55, 0.1);
      transition: width var(--transition-normal);
      z-index: 1;
    }

    .poll-option-fill {
      height: 100%;
      background: linear-gradient(90deg, 
        rgba(212, 175, 55, 0.3) 0%, 
        rgba(212, 175, 55, 0.1) 100%);
      transition: width var(--transition-normal);
    }

    .poll-vote-btn {
      background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
      color: var(--neutral-900);
      border: none;
      border-radius: var(--radius-lg);
      padding: var(--space-3) var(--space-4);
      font-weight: 600;
      cursor: pointer;
      transition: all var(--transition-normal);
      display: flex;
      align-items: center;
      gap: var(--space-2);
    }

    .poll-vote-btn:hover {
      background: linear-gradient(135deg, var(--primary-gold-light), var(--primary-gold));
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(212, 175, 55, 0.4);
    }

    .poll-vote-btn:disabled {
      opacity: 0.7;
      cursor: not-allowed;
      transform: none;
    }

    .poll-stats {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.9rem;
      color: var(--neutral-400);
    }

    .poll-total-votes {
      display: flex;
      align-items: center;
      gap: var(--space-2);
    }

    .poll-voted-indicator {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      color: var(--primary-gold);
      font-weight: 600;
    }

    /* Poll Indicator in Composer */
    .poll-indicator {
      display: none;
      background: linear-gradient(135deg, 
        rgba(212, 175, 55, 0.1) 0%, 
        rgba(212, 175, 55, 0.05) 100%);
      border: 1px solid rgba(212, 175, 55, 0.3);
      border-radius: var(--radius-lg);
      padding: var(--space-4);
      margin-bottom: var(--space-4);
    }

    .poll-preview-container {
      color: var(--primary-gold);
    }

    .poll-preview-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--space-3);
    }

    .poll-preview-title {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      font-weight: 600;
      font-size: 1.1rem;
    }

    .poll-preview-title i {
      font-size: 1.2rem;
      opacity: 0.8;
    }

    .poll-preview-options {
      margin-bottom: var(--space-3);
    }

    .poll-preview-option {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      padding: var(--space-2) var(--space-3);
      background: rgba(0, 0, 0, 0.2);
      border-radius: var(--radius-md);
      margin-bottom: var(--space-2);
      font-size: 0.95rem;
      color: var(--neutral-200);
    }

    .poll-preview-expiry {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      font-size: 0.875rem;
      color: var(--neutral-400);
      margin-top: var(--space-2);
    }

    .poll-remove-btn {
      background: rgba(220, 53, 69, 0.2);
      color: #dc3545;
      border: none;
      border-radius: var(--radius-md);
      padding: var(--space-2);
      cursor: pointer;
      transition: all var(--transition-normal);
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      flex-shrink: 0;
    }

    .poll-remove-btn:hover {
      background: rgba(220, 53, 69, 0.3);
      transform: scale(1.1);
    }

    /* Post Actions */
    .post-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: var(--space-4);
      border-top: 1px solid var(--glass-border);
    }

    .post-reactions {
      display: flex;
      gap: var(--space-2);
    }

    .reaction-btn {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-4);
      border: 1px solid var(--glass-border);
      background: transparent;
      color: var(--neutral-400);
      border-radius: var(--radius-lg);
      cursor: pointer;
      transition: all var(--transition-normal);
      font-size: 0.875rem;
      font-weight: 500;
    }

    .reaction-btn:hover {
      background: rgba(255, 255, 255, 0.05);
      border-color: var(--primary-gold);
      color: var(--primary-gold);
      transform: translateY(-1px);
    }

    .reaction-btn.active {
      background: rgba(212, 175, 55, 0.1);
      border-color: var(--primary-gold);
      color: var(--primary-gold);
    }

    .reaction-btn.like.active {
      background: rgba(34, 197, 94, 0.1);
      border-color: #22c55e;
      color: #22c55e;
    }

    .reaction-btn.celebrate.active {
      background: rgba(245, 158, 11, 0.1);
      border-color: #f59e0b;
      color: #f59e0b;
    }

    .reaction-btn.angry.active {
      background: rgba(239, 68, 68, 0.1);
      border-color: #ef4444;
      color: #ef4444;
    }

    .post-share {
      display: flex;
      gap: var(--space-2);
    }

    .share-btn {
      width: 36px;
      height: 36px;
      border: 1px solid var(--glass-border);
      background: transparent;
      color: var(--neutral-400);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all var(--transition-normal);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .share-btn:hover {
      background: var(--bg-card-hover);
      border-color: var(--primary-gold);
      color: var(--primary-gold);
      transform: translateY(-1px);
    }

    /* Sidebar */
    .forum-sidebar {
      position: sticky;
      top: var(--space-6);
    }

    .sidebar-section {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-2xl);
      padding: var(--space-6);
      margin-bottom: var(--space-6);
      box-shadow: var(--glass-shadow);
    }

    .sidebar-title {
      font-family: var(--font-display);
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--primary-gold);
      margin-bottom: var(--space-4);
      text-align: center;
    }

    /* Live Activity */
    .live-activity-item {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      padding: var(--space-3);
      border-radius: var(--radius-lg);
      margin-bottom: var(--space-2);
      transition: var(--transition-normal);
    }

    .live-activity-item:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .activity-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      border: 2px solid var(--primary-gold);
      object-fit: cover;
    }

    .activity-text {
      flex: 1;
      font-size: 0.875rem;
      color: var(--neutral-300);
    }

    .activity-time {
      font-size: 0.75rem;
      color: var(--neutral-500);
    }

    /* Trending Topics */
    .trending-topic {
      padding: var(--space-3);
      border-radius: var(--radius-lg);
      margin-bottom: var(--space-2);
      cursor: pointer;
      transition: var(--transition-normal);
    }

    .trending-topic:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .trending-title {
      font-weight: 600;
      color: var(--neutral-200);
      margin-bottom: var(--space-1);
    }

    .trending-meta {
      font-size: 0.75rem;
      color: var(--neutral-500);
    }

    /* Reddit Feeds */
    .reddit-feed-item {
      padding: var(--space-3);
      border-radius: var(--radius-lg);
      margin-bottom: var(--space-2);
      cursor: pointer;
      transition: var(--transition-normal);
      border-left: 3px solid transparent;
    }

    .reddit-feed-item:hover {
      background: rgba(255, 255, 255, 0.05);
      border-left-color: #ff4500;
    }

    .reddit-title {
      font-weight: 600;
      color: var(--neutral-200);
      font-size: 0.875rem;
      line-height: 1.4;
      margin-bottom: var(--space-1);
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .reddit-meta {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      font-size: 0.75rem;
      color: var(--neutral-500);
      margin-bottom: var(--space-1);
    }

    .reddit-subreddit {
      background: #ff4500;
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-weight: 500;
    }

    .reddit-author {
      color: var(--neutral-400);
    }

    .reddit-time {
      margin-left: auto;
    }

    .reddit-preview {
      font-size: 0.75rem;
      color: var(--neutral-400);
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .reddit-stats {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      margin-top: var(--space-1);
      font-size: 0.75rem;
      color: var(--neutral-500);
    }

    .reddit-stat {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .reddit-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--space-4);
      color: var(--neutral-400);
      font-size: 0.875rem;
    }

    .reddit-error {
      padding: var(--space-3);
      background: rgba(239, 68, 68, 0.1);
      border: 1px solid rgba(239, 68, 68, 0.2);
      border-radius: var(--radius-lg);
      color: #fca5a5;
      font-size: 0.875rem;
      text-align: center;
    }

    .reddit-refresh {
      background: none;
      border: none;
      color: var(--primary-gold);
      cursor: pointer;
      font-size: 0.75rem;
      padding: 2px 6px;
      border-radius: 4px;
      transition: var(--transition-normal);
    }

    .reddit-refresh:hover {
      background: rgba(212, 175, 55, 0.1);
    }

    /* Online Users */
    .online-users-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(40px, 1fr));
      gap: var(--space-2);
    }

    .online-user {
      position: relative;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 2px solid var(--primary-gold);
      object-fit: cover;
      cursor: pointer;
      transition: var(--transition-normal);
    }

    .online-user:hover {
      transform: scale(1.1);
      box-shadow: 0 4px 16px rgba(212, 175, 55, 0.4);
    }

    .online-indicator {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 12px;
      height: 12px;
      background: #10b981;
      border: 2px solid var(--bg-primary);
      border-radius: 50%;
    }

    /* Loading States */
    .loading-posts {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--space-8);
      color: var(--neutral-400);
    }

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid var(--glass-border);
      border-top: 3px solid var(--primary-gold);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: var(--space-3);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Typing Indicator */
    .typing-indicator {
      display: none;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-3);
      background: rgba(255, 255, 255, 0.05);
      border-radius: var(--radius-lg);
      margin-bottom: var(--space-4);
      color: var(--neutral-400);
      font-size: 0.875rem;
      font-style: italic;
    }

    .typing-dots {
      display: flex;
      gap: 2px;
    }

    .typing-dot {
      width: 4px;
      height: 4px;
      background: var(--primary-gold);
      border-radius: 50%;
      animation: typing 1.4s infinite;
    }

    .typing-dot:nth-child(2) {
      animation-delay: 0.2s;
    }

    .typing-dot:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes typing {
      0%, 60%, 100% {
        transform: translateY(0);
      }
      30% {
        transform: translateY(-10px);
      }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .forum-container {
        padding: var(--space-4);
      }

      .forum-title {
        font-size: 2.5rem;
      }

      .game-tabs-wrapper {
        flex-direction: column;
        gap: 1rem;
      }

      .game-tabs {
        width: 100%;
        justify-content: center;
      }

      .game-tab {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
      }

      .game-tab span {
        font-size: 0.8rem;
      }

      .post-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
      }

      .post-actions {
        flex-direction: column;
        gap: var(--space-3);
        align-items: stretch;
      }

      .post-reactions {
        justify-content: center;
      }

      .composer-actions {
        flex-direction: column;
        gap: var(--space-3);
        align-items: stretch;
      }

      .composer-tools {
        justify-content: center;
      }
    }

    /* Game-specific theming */
    .forum-container.wc1-theme {
      --game-primary: #daa520;
      --game-secondary: #8b4513;
    }

    .forum-container.wc2-theme {
      --game-primary: var(--primary-gold);
      --game-secondary: var(--horde-red);
    }

    .forum-container.wc3-theme {
      --game-primary: var(--alliance-blue);
      --game-secondary: #8a2be2;
    }

    /* New post animation */
    @keyframes slideInNew {
      0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
      }
      100% {
        opacity: 1;
        transform: translateY(0) scale(1);
      }
    }

    .post-card.new-post {
      animation: slideInNew 0.5s ease-out;
    }

    /* Success states */
    .post-success {
      background: rgba(34, 197, 94, 0.1);
      border-color: #22c55e;
    }
  </style>
</head>
<body>
  <!-- Navbar -->
  <div id="navbar-container"></div>

  <main class="forum-container" id="forum-container">
    <!-- Forum Header -->
    <header class="forum-header">
              <h1 class="forum-title" data-theme="forum">📜 Stone Tablet</h1>
    </header>

    <!-- Game Selector Tabs -->
    <section class="game-tabs-container">
      <div class="game-tabs-wrapper">
        <div class="game-tabs">
          <button class="game-tab" data-game="wc1" data-game-full="Warcraft I">
            <i class="fas fa-dragon"></i>
            <span>WC I</span>
          </button>
          <button class="game-tab active" data-game="wc2" data-game-full="Warcraft II">
            <i class="fas fa-shield-alt"></i>
            <span>WC II</span>
          </button>
          <button class="game-tab" data-game="wc3" data-game-full="Warcraft III">
            <i class="fas fa-chess-king"></i>
            <span>WC III</span>
          </button>
        </div>

        <!-- Forum Stats -->
        <div class="forum-stats">
          <div class="forum-stat">
            <div class="forum-stat-value" id="active-posts-count">0</div>
            <div class="forum-stat-label">Active Posts</div>
          </div>
          <div class="forum-stat">
            <div class="forum-stat-value" id="online-users-count">0</div>
            <div class="forum-stat-label">Online Now</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Main Forum Layout -->
    <div class="forum-main">
      <!-- Posts Feed -->
      <div class="forum-feed">
        <!-- Post Composer -->
        <div class="post-composer hidden" id="post-composer">
          <div class="composer-header">
            <img src="/assets/img/default-avatar.svg" alt="Your Avatar" class="composer-avatar" id="composer-avatar">
            <div class="composer-input-area">
              <textarea 
                class="composer-textarea" 
                id="composer-textarea"
                placeholder="Share your latest strategy, epic victory, or legendary moments with the community..."
                rows="4"
              ></textarea>
            </div>
          </div>
          
                      <div class="composer-actions">
            <div class="composer-tools">
              <button class="composer-tool" id="attach-image-btn" title="Attach Image">
                <i class="fas fa-image"></i>
              </button>
              <button class="composer-tool" id="attach-video-btn" title="Attach Video">
                <i class="fas fa-video"></i>
              </button>
              <button class="composer-tool" id="add-poll-btn" title="Create Poll">
                <i class="fas fa-poll"></i>
              </button>
              <button class="composer-tool" id="add-emoji-btn" title="Add Emoji">
                <i class="fas fa-smile"></i>
              </button>
            </div>
            
            <div class="composer-buttons">
              <button class="cancel-post-button" id="cancel-post-btn">
                <i class="fas fa-times"></i>
                Cancel
              </button>
              <button class="post-button" id="publish-post-btn" disabled>
                <i class="fas fa-paper-plane"></i>
                Share with Community
              </button>
            </div>
          </div>
        </div>

        <!-- Typing Indicator -->
        <div class="typing-indicator" id="typing-indicator">
          <div class="typing-dots">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
          </div>
          <span id="typing-users">Someone is crafting a legendary post...</span>
        </div>

        <!-- Live Posts Feed -->
        <div class="posts-feed" id="posts-feed">
          <!-- Loading State -->
          <div class="loading-posts" id="loading-posts">
            <div class="loading-spinner"></div>
            <span>Loading epic discussions...</span>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <aside class="forum-sidebar">
        <!-- Live Activity -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">
            <i class="fas fa-bolt"></i>
            Live Activity
          </h3>
          <div id="live-activity">
            <!-- Live activity items will be populated here -->
          </div>
        </div>



        <!-- Reddit Community -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">
            <i class="fab fa-reddit"></i>
            Community Buzz
          </h3>
          <div id="reddit-feeds">
            <!-- Reddit feeds will be populated here -->
          </div>
        </div>

        <!-- Online Warriors -->
        <div class="sidebar-section">
          <h3 class="sidebar-title">
            <i class="fas fa-users"></i>
            Online Warriors
          </h3>
          <div class="online-users-grid" id="online-users">
            <!-- Online users will be populated here -->
          </div>
        </div>
      </aside>
    </div>
  </main>

  <!-- Hidden file input for image uploads -->
  <input type="file" id="image-upload" accept="image/*" style="display: none;">
  <!-- Video upload removed - using embed functionality instead -->

  <!-- Scripts -->
  <script src="/js/utils.js"></script>
  <script src="/js/api-config.js"></script>
  <script type="module" src="/js/app.js"></script>
  <script src="/js/main.js"></script>
  <script type="module">
    // Import modules
    import { ForumCore } from '/js/modules/ForumCore.js';
    import { LiveForumManager } from '/js/modules/LiveForumManager.js';
    import { RedditFeedManager } from '/js/modules/RedditFeedManager.js';
    
    // Initialize the next-gen forum
    window.addEventListener('load', async () => {
      console.log('🚀 Initializing Next-Gen Forum...');
      
      // Initialize forum functionality first
      await initializeForum();
      
      // Load unified navigation and update profile
      console.log('🔄 Initializing navigation on forum page...');

      // Load unified navigation
      if (typeof window.loadNavigation === 'function') {
        await window.loadNavigation();
      } else if (typeof window.loadNavbar === 'function') {
        await window.loadNavbar();
      }

      // Update navbar profile
      setTimeout(async () => {
        if (window.updateNavbarProfileUnified) {
          console.log('🔄 Updating navbar profile (unified) on forum page');
          await window.updateNavbarProfileUnified();
        } else if (window.updateNavbarProfile) {
          console.log('🔄 Updating navbar profile (legacy) on forum page');
          await window.updateNavbarProfile();
        }
      }, 1000); // 1 second delay after page load
    });
  </script>
</body>
</html>
