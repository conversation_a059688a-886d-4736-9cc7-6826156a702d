<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download WC Arena Core Launcher</title>
    
    <!-- Core Stylesheets -->
    <link rel="stylesheet" href="/css/warcraft-app-modern.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700;800&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .download-container {
            max-width: 900px;
            margin: 0 auto;
            padding: var(--space-8);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .download-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .download-title {
            font-family: var(--font-display);
            font-size: 3.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--space-3);
            text-shadow: 0 0 40px rgba(212, 175, 55, 0.6);
        }

        .download-subtitle {
            color: var(--neutral-300);
            font-size: 1.25rem;
            font-weight: 500;
            margin-bottom: var(--space-6);
        }

        .launcher-preview {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            margin-bottom: var(--space-8);
            box-shadow: var(--glass-shadow);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            text-align: center;
            transition: all var(--transition-normal);
        }

        .feature-card:hover {
            background: rgba(255, 255, 255, 0.08);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: var(--primary-gold);
            margin-bottom: var(--space-4);
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--neutral-100);
            margin-bottom: var(--space-2);
        }

        .feature-description {
            color: var(--neutral-300);
            line-height: 1.6;
        }

        .download-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-2xl);
            padding: var(--space-8);
            text-align: center;
            box-shadow: var(--glass-shadow);
        }

        .download-btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-3);
            background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-light));
            color: var(--neutral-900);
            padding: var(--space-4) var(--space-8);
            border-radius: var(--radius-xl);
            font-size: 1.125rem;
            font-weight: 600;
            text-decoration: none;
            transition: all var(--transition-normal);
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(212, 175, 55, 0.4);
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(212, 175, 55, 0.6);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--primary-gold);
            text-decoration: none;
            margin-bottom: var(--space-6);
            transition: all var(--transition-normal);
        }

        .back-link:hover {
            color: var(--primary-gold-light);
            transform: translateX(-4px);
        }
    </style>
  <!-- Universal Navbar Consistency - MUST be loaded last for proper override -->`r`n  <link rel=stylesheet href=/css/navbar-universal.css />`r`n</head>
<body>
    <!-- Navbar -->
    <div id="navbar-container"></div>

    <main class="download-container">
        <a href="/" class="back-link">
            <i class="fas fa-arrow-left"></i>
            Back to Arena
        </a>

        <div class="download-header">
            <h1 class="download-title">⚙️ WC Arena Core Launcher</h1>
            <p class="download-subtitle">
                The ultimate system tray companion for all your Warcraft games
            </p>
        </div>

        <div class="launcher-preview">
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="feature-title">Auto Game Detection</h3>
                    <p class="feature-description">
                        Automatically finds all Warcraft I, II, III, and Battle.net installations
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3 class="feature-title">Quick Launch</h3>
                    <p class="feature-description">
                        Launch any detected game directly from your system tray
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-window-minimize"></i>
                    </div>
                    <h3 class="feature-title">System Tray Integration</h3>
                    <p class="feature-description">
                        Minimize to tray and keep running in the background
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3 class="feature-title">Arena Integration</h3>
                    <p class="feature-description">
                        Direct access to WC Arena Core features and community
                    </p>
                </div>
            </div>
        </div>

        <div class="download-section">
            <h2 style="margin-bottom: var(--space-6); color: var(--neutral-100);">Download Launcher</h2>
            
            <a href="http://localhost:3000/warcraft-arena-launcher-setup.exe" class="download-btn" download>
                <i class="fas fa-download"></i>
                Download WC Arena Core Launcher
            </a>
            
            <p style="margin-top: var(--space-4); color: var(--neutral-400);">
                Windows installer with automatic dependencies. No additional software required!
            </p>
        </div>
    </main>

    <!-- Scripts -->
    <script src="/js/utils.js"></script>
    <script src="/js/api-config.js"></script>
    <script src="/js/main.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            // Load navbar
            try {
                const navbarResponse = await fetch('/components/navbar.html');
                if (navbarResponse.ok) {
                    const navbarHtml = await navbarResponse.text();
                    document.getElementById('navbar-container').innerHTML = navbarHtml;
                    
                    // Load and execute navbar script
                    const navbarScript = document.createElement('script');
                    navbarScript.src = '/js/navbar-modern.js';
                    navbarScript.onload = () => {
                        if (window.initModernNavbar) {
                            window.initModernNavbar();
                        }
                    };
                    document.head.appendChild(navbarScript);
                }
            } catch (error) {
                console.warn('Could not load navbar:', error);
            }
        });
    </script>
</body>
</html> 
