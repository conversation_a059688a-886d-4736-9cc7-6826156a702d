/**
 * Game Manager Page JavaScript
 * Handles functionality for the integrated game management interface
 */

import platformManager from './modules/PlatformManager.js';

class GameManagerPage {
  constructor() {
    // Use a WeakMap for private state
    this._state = new WeakMap();
    this._state.set(this, {
      initialized: false,
      currentView: 'overview',
      screenshots: [],
      stats: {},
      settings: {
        autoAnalysis: true,
        autoReport: false,
        confidenceThreshold: 70,
        gameTypes: ['warcraft1', 'warcraft2', 'warcraft3'],
        notifications: true
      }
    });
    
    // Use lazy loading for heavy components
    this._components = {
      screenshotManager: null,
      statsManager: null,
      settingsManager: null
    };
    
    console.log('🎮 Enhanced GameManagerPage created');
  }

  async init() {
    const state = this._state.get(this);
    if (state.initialized) {
      console.log('⚠️ GameManagerPage already initialized');
      return;
    }

    console.log('🚀 Initializing enhanced game manager...', {
      isDesktop: platformManager.isDesktopApp(),
      platform: platformManager.getPlatform(),
      features: platformManager.getFeatures()
    });
    
    // Check if we're in desktop app
    if (!platformManager.isDesktopApp()) {
      this._showDownloadPrompt();
      return;
    }
    
    try {
      // Initialize core functionality in parallel
      await Promise.all([
        this._initializeUI(),
        this._initializeSettings(),
        platformManager.hasFeature('screenshotCapture') && this._initializeScreenshots(),
        platformManager.hasFeature('matchTracking') && this._initializeStats()
      ].filter(Boolean));
      
      // Setup event listeners after core initialization
      this._setupEventListeners();
      
      state.initialized = true;
      console.log('✅ Enhanced game manager ready');
    } catch (error) {
      console.error('❌ Game manager initialization failed:', error);
      this._handleInitError(error);
    }
  }

  // Private methods
  _showDownloadPrompt() {
    const container = document.getElementById('game-manager-content');
    if (!container) return;
    
    container.innerHTML = `
      <div class="download-prompt glass-card">
        <div class="download-content">
          <h2>Download WC Arena Companion</h2>
          <p>To access the Arena Core features like game detection, automatic screenshot capture, and match tracking, please download our desktop companion app.</p>
          <div class="feature-list">
            <div class="feature-item">
              <i class="fas fa-gamepad"></i>
              <span>Automatic Game Detection</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-camera"></i>
              <span>Screenshot Capture</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-chart-line"></i>
              <span>Match Tracking</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-bell"></i>
              <span>System Notifications</span>
            </div>
          </div>
          <div class="download-actions">
            <a href="/download-launcher.html" class="btn btn-primary">
              <i class="fas fa-download"></i>
              Download Now
            </a>
            <p class="platform-note">Available for Windows, macOS, and Linux</p>
          </div>
        </div>
      </div>
    `;
  }

  async _initializeUI() {
    // Wait for navbar with timeout
    await Promise.race([
      new Promise(resolve => {
        const checkNavbar = () => {
          const navbar = document.querySelector('.navbar-modern');
          if (navbar) resolve();
          else setTimeout(checkNavbar, 100);
        };
        checkNavbar();
      }),
      new Promise((_, reject) => setTimeout(() => reject(new Error('Navbar timeout')), 5000))
    ]);
    
    // Initialize navbar if available
    if (window.initModernNavbar) {
      window.initModernNavbar();
    }
  }

  async _initializeSettings() {
    // Lazy load settings manager
    if (!this._components.settingsManager) {
      const { SettingsManager } = await import('./modules/SettingsManager.js');
      this._components.settingsManager = new SettingsManager();
    }
    await this._components.settingsManager.init();
  }

  async _initializeScreenshots() {
    // Only initialize if screenshot feature is available
    if (!platformManager.hasFeature('screenshotCapture')) return;
    
    // Lazy load screenshot manager
    if (!this._components.screenshotManager) {
      const { ScreenshotManager } = await import('./modules/ScreenshotManager.js');
      this._components.screenshotManager = new ScreenshotManager();
    }
    await this._components.screenshotManager.init();
  }

  async _initializeStats() {
    // Only initialize if match tracking feature is available
    if (!platformManager.hasFeature('matchTracking')) return;
    
    // Lazy load stats manager
    if (!this._components.statsManager) {
      const { StatsManager } = await import('./modules/StatsManager.js');
      this._components.statsManager = new StatsManager();
    }
    await this._components.statsManager.init();
  }

  _handleInitError(error) {
    const container = document.getElementById('game-manager-content');
    if (!container) return;
    
    container.innerHTML = `
      <div class="error-state glass-card">
        <h2>Initialization Error</h2>
        <p>Failed to initialize Arena Core. Please try refreshing the page or reinstalling the app.</p>
        <pre>${error.message}</pre>
        <div class="error-actions">
          <button onclick="location.reload()" class="btn btn-primary">
            <i class="fas fa-sync"></i>
            Refresh Page
          </button>
          <a href="/download-launcher.html" class="btn btn-secondary">
            <i class="fas fa-download"></i>
            Reinstall App
          </a>
        </div>
      </div>
    `;
  }

  setupEventListeners() {
    // Enhanced navigation
    document.addEventListener('click', (e) => {
      if (e.target.matches('[data-view]')) {
        e.preventDefault();
        this.switchView(e.target.dataset.view);
      }

      if (e.target.matches('[data-action]')) {
        e.preventDefault();
        this.handleAction(e.target.dataset.action, e.target);
      }
    });

    // Settings form handler
    document.addEventListener('submit', (e) => {
      if (e.target.matches('#screenshot-settings-form')) {
        e.preventDefault();
        this.saveSettings(new FormData(e.target));
      }
    });

    // Manual report form handler
    document.addEventListener('submit', (e) => {
      if (e.target.matches('#manual-report-form')) {
        e.preventDefault();
        this.submitManualReport(new FormData(e.target));
      }
    });

    // Real-time updates via WebSocket (if available)
    if (window.io) {
      this.setupWebSocketListeners();
    }
  }

  setupWebSocketListeners() {
    // Listen for real-time screenshot analysis updates
    window.io.on('screenshot_analyzed', (data) => {
      this.handleRealtimeUpdate(data);
    });

    window.io.on('match_reported', (data) => {
      this.handleMatchReported(data);
    });
  }

  async loadUserSettings() {
    try {
      const response = await fetch('/api/user-settings?category=screenshots');
      if (response.ok) {
        const data = await response.json();
        this.settings = { ...this.settings, ...data.screenshots };
        console.log('⚙️ User settings loaded');
      }
    } catch (error) {
      console.error('❌ Error loading user settings:', error);
    }
  }

  async loadScreenshotHistory() {
    try {
      const response = await fetch('/api/screenshot-history?limit=20');
      if (response.ok) {
        const data = await response.json();
        this.screenshots = data.screenshots || [];
        console.log(`📸 Loaded ${this.screenshots.length} screenshots`);
      }
    } catch (error) {
      console.error('❌ Error loading screenshot history:', error);
    }
  }

  async loadStatistics() {
    try {
      // Get Electron app stats if available
      if (window.electronAPI) {
        this.stats = await window.electronAPI.screenshots.getStats();
      } else {
        // Fallback to web stats
        const response = await fetch('/api/screenshot-stats');
        if (response.ok) {
          this.stats = await response.json();
        }
      }
      console.log('📊 Statistics loaded:', this.stats);
    } catch (error) {
      console.error('❌ Error loading statistics:', error);
      this.stats = this.getDefaultStats();
    }
  }

  getDefaultStats() {
    return {
      detected: 0,
      analyzed: 0,
      victories: 0,
      defeats: 0,
      autoReported: 0,
      averageConfidence: 0,
      uptime: 0,
      isMonitoring: false
    };
  }

  renderInterface() {
    const container = document.getElementById('game-manager-content');
    if (!container) {
      console.error('❌ Game manager container not found');
      return;
    }

    container.innerHTML = this.getMainHTML();
    this.switchView(this.currentView);
  }

  getMainHTML() {
    return `
      <div class="enhanced-game-manager">
        <!-- Enhanced Header with Real-time Status -->
        <div class="gm-header">
          <div class="status-indicators">
            <div class="status-item ${this.stats.isMonitoring ? 'active' : 'inactive'}">
              <span class="status-dot"></span>
              <span>Screenshot Monitoring</span>
            </div>
            <div class="status-item ${this.settings.autoAnalysis ? 'active' : 'inactive'}">
              <span class="status-dot"></span>
              <span>Auto Analysis</span>
            </div>
            <div class="status-item ${this.settings.autoReport ? 'active' : 'inactive'}">
              <span class="status-dot"></span>
              <span>Auto Reporting</span>
            </div>
          </div>
          <div class="stats-summary">
            <div class="stat-box">
              <div class="stat-number">${this.stats.analyzed || 0}</div>
              <div class="stat-label">Analyzed</div>
            </div>
            <div class="stat-box">
              <div class="stat-number">${this.stats.victories || 0}</div>
              <div class="stat-label">Victories</div>
            </div>
            <div class="stat-box">
              <div class="stat-number">${this.stats.defeats || 0}</div>
              <div class="stat-label">Defeats</div>
            </div>
            <div class="stat-box">
              <div class="stat-number">${Math.round(this.stats.averageConfidence || 0)}%</div>
              <div class="stat-label">Avg Confidence</div>
            </div>
          </div>
        </div>

        <!-- Enhanced Navigation -->
        <nav class="gm-navigation">
          <button class="nav-btn ${this.currentView === 'overview' ? 'active' : ''}" data-view="overview">
            📊 Overview
          </button>
          <button class="nav-btn ${this.currentView === 'screenshots' ? 'active' : ''}" data-view="screenshots">
            📸 Screenshots
          </button>
          <button class="nav-btn ${this.currentView === 'settings' ? 'active' : ''}" data-view="settings">
            ⚙️ Settings
          </button>
          <button class="nav-btn ${this.currentView === 'history' ? 'active' : ''}" data-view="history">
            📋 History
          </button>
        </nav>

        <!-- Dynamic Content Area -->
        <div class="gm-content" id="gm-dynamic-content">
          <!-- Content will be rendered here -->
        </div>

        <!-- Enhanced Action Bar -->
        <div class="gm-actions">
          <button class="action-btn primary" data-action="refresh">
            🔄 Refresh Data
          </button>
          <button class="action-btn secondary" data-action="open-folder">
            📁 Open Screenshot Folder
          </button>
          <button class="action-btn tertiary" data-action="test-analysis">
            🧪 Test Analysis
          </button>
        </div>
      </div>
    `;
  }

  switchView(viewName) {
    this.currentView = viewName;
    
    // Update navigation
    document.querySelectorAll('.nav-btn').forEach(btn => {
      btn.classList.toggle('active', btn.dataset.view === viewName);
    });

    // Render view content
    const contentContainer = document.getElementById('gm-dynamic-content');
    if (contentContainer) {
      contentContainer.innerHTML = this.getViewHTML(viewName);
    }

    console.log(`📱 Switched to view: ${viewName}`);
  }

  getViewHTML(viewName) {
    switch (viewName) {
      case 'overview':
        return this.getOverviewHTML();
      case 'screenshots':
        return this.getScreenshotsHTML();
      case 'settings':
        return this.getSettingsHTML();
      case 'history':
        return this.getHistoryHTML();
      default:
        return '<div class="error">Unknown view</div>';
    }
  }

  getOverviewHTML() {
    const winRate = this.stats.victories + this.stats.defeats > 0 
      ? Math.round((this.stats.victories / (this.stats.victories + this.stats.defeats)) * 100)
      : 0;

    return `
      <div class="overview-dashboard">
        <div class="dashboard-grid">
          
          <!-- Real-time Activity Feed -->
          <div class="dashboard-card activity-feed">
            <h3>🔴 Live Activity</h3>
            <div class="activity-list" id="activity-feed">
              ${this.getRecentActivityHTML()}
            </div>
          </div>

          <!-- Advanced Statistics -->
          <div class="dashboard-card stats-advanced">
            <h3>📈 Advanced Statistics</h3>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">Win Rate</span>
                <span class="stat-value">${winRate}%</span>
                <div class="stat-bar">
                  <div class="stat-fill" style="width: ${winRate}%"></div>
                </div>
              </div>
              <div class="stat-item">
                <span class="stat-label">Detection Accuracy</span>
                <span class="stat-value">${Math.round(this.stats.averageConfidence || 0)}%</span>
                <div class="stat-bar">
                  <div class="stat-fill" style="width: ${this.stats.averageConfidence || 0}%"></div>
                </div>
              </div>
              <div class="stat-item">
                <span class="stat-label">Auto-Reports</span>
                <span class="stat-value">${this.stats.autoReported || 0}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Session Uptime</span>
                <span class="stat-value">${this.formatUptime(this.stats.uptime)}</span>
              </div>
            </div>
          </div>

          <!-- Game Type Breakdown -->
          <div class="dashboard-card game-breakdown">
            <h3>🎮 Game Analysis</h3>
            <div class="game-stats">
              ${this.getGameStatsHTML()}
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="dashboard-card quick-actions">
            <h3>⚡ Quick Actions</h3>
            <div class="action-grid">
              <button class="quick-action-btn" data-action="start-monitoring">
                <span class="icon">🔍</span>
                <span class="label">Start Monitoring</span>
              </button>
              <button class="quick-action-btn" data-action="manual-screenshot">
                <span class="icon">📸</span>
                <span class="label">Take Screenshot</span>
              </button>
              <button class="quick-action-btn" data-action="report-match">
                <span class="icon">📋</span>
                <span class="label">Report Match</span>
              </button>
              <button class="quick-action-btn" data-action="view-folder">
                <span class="icon">📁</span>
                <span class="label">Open Folder</span>
              </button>
            </div>
          </div>

        </div>
      </div>
    `;
  }

  getScreenshotsHTML() {
    return `
      <div class="screenshots-view">
        
        <!-- Enhanced Filters -->
        <div class="screenshots-filters">
          <div class="filter-group">
            <label>Game Type:</label>
            <select id="game-filter">
              <option value="">All Games</option>
              <option value="warcraft1">Warcraft 1</option>
              <option value="warcraft2">Warcraft 2</option>
              <option value="warcraft3">Warcraft 3</option>
            </select>
          </div>
          <div class="filter-group">
            <label>Result:</label>
            <select id="result-filter">
              <option value="">All Results</option>
              <option value="victory">Victories</option>
              <option value="defeat">Defeats</option>
            </select>
          </div>
          <div class="filter-group">
            <label>Confidence:</label>
            <input type="range" id="confidence-filter" min="0" max="100" value="0">
            <span id="confidence-value">0%+</span>
          </div>
        </div>

        <!-- Screenshots Grid -->
        <div class="screenshots-grid" id="screenshots-container">
          ${this.getScreenshotCardsHTML()}
        </div>

        <!-- Enhanced Pagination -->
        <div class="screenshots-pagination">
          <button class="page-btn" data-action="prev-page">← Previous</button>
          <span class="page-info">Page 1 of 1</span>
          <button class="page-btn" data-action="next-page">Next →</button>
        </div>

      </div>
    `;
  }

  getScreenshotCardsHTML() {
    if (this.screenshots.length === 0) {
      return `
        <div class="no-screenshots">
          <div class="no-screenshots-icon">📸</div>
          <div class="no-screenshots-title">No Screenshots Found</div>
          <div class="no-screenshots-subtitle">
            Take screenshots during your Warcraft games, and they'll appear here with AI analysis!
          </div>
          <button class="action-btn primary" data-action="start-monitoring">
            Start Monitoring
          </button>
        </div>
      `;
    }

    return this.screenshots.map(screenshot => `
      <div class="screenshot-card ${screenshot.result}" data-confidence="${screenshot.confidence}">
        
        <!-- Screenshot Preview -->
        <div class="screenshot-preview">
          <img src="${screenshot.imagePath}" alt="Game Screenshot" loading="lazy">
          <div class="screenshot-overlay">
            <div class="result-badge ${screenshot.result}">
              ${screenshot.result === 'victory' ? '🏆' : '💔'} ${screenshot.result.toUpperCase()}
            </div>
            <div class="confidence-badge">
              ${Math.round(screenshot.confidence)}% confident
            </div>
          </div>
        </div>

        <!-- Enhanced Analysis Info -->
        <div class="screenshot-info">
          <div class="info-header">
            <span class="game-type">${this.formatGameType(screenshot.gameType)}</span>
            <span class="timestamp">${this.formatTimestamp(screenshot.createdAt)}</span>
          </div>

          <!-- Player Information -->
          ${screenshot.players && screenshot.players.length > 0 ? `
            <div class="players-info">
              <strong>👥 Players:</strong>
              <div class="players-list">
                ${screenshot.players.map(player => `
                  <span class="player-tag">${player.name}</span>
                `).join('')}
              </div>
            </div>
          ` : ''}

          <!-- Map Information -->
          ${screenshot.mapName ? `
            <div class="map-info">
              <strong>🗺️ Map:</strong> ${screenshot.mapName}
            </div>
          ` : ''}

          <!-- Duration Information -->
          ${screenshot.duration ? `
            <div class="duration-info">
              <strong>⏱️ Duration:</strong> ${screenshot.duration}
            </div>
          ` : ''}

          <!-- Analysis Methods -->
          ${screenshot.analysis && screenshot.analysis.methods ? `
            <div class="analysis-methods">
              <strong>🔍 Detection:</strong>
              <div class="methods-tags">
                ${screenshot.analysis.methods.map(method => `
                  <span class="method-tag">${this.formatMethod(method)}</span>
                `).join('')}
              </div>
            </div>
          ` : ''}

          <!-- Achievements -->
          ${screenshot.achievements && screenshot.achievements.length > 0 ? `
            <div class="achievements">
              <strong>🏆 Achievements:</strong>
              <div class="achievement-list">
                ${screenshot.achievements.map(achievement => `
                  <span class="achievement-badge">${achievement}</span>
                `).join('')}
              </div>
            </div>
          ` : ''}

        </div>

        <!-- Action Buttons -->
        <div class="screenshot-actions">
          <button class="action-btn small ${screenshot.reportedToLadder ? 'reported' : 'primary'}" 
                  data-action="report-screenshot" 
                  data-screenshot-id="${screenshot.id}"
                  ${screenshot.reportedToLadder ? 'disabled' : ''}>
            ${screenshot.reportedToLadder ? '✅ Reported' : '📋 Report to Ladder'}
          </button>
          <button class="action-btn small secondary" data-action="view-details" data-screenshot-id="${screenshot.id}">
            👁️ Details
          </button>
          <button class="action-btn small tertiary" data-action="reanalyze" data-screenshot-id="${screenshot.id}">
            🔄 Re-analyze
          </button>
        </div>

      </div>
    `).join('');
  }

  getGameStatsHTML() {
    const stats = {};
    this.screenshots.forEach(s => {
      if (!stats[s.gameType]) {
        stats[s.gameType] = { victories: 0, defeats: 0, confidence: 0, count: 0 };
      }
      stats[s.gameType].count++;
      if (s.result === 'victory') {
        stats[s.gameType].victories++;
      } else if (s.result === 'defeat') {
        stats[s.gameType].defeats++;
      }
      if (s.analysis && s.analysis.confidence) {
        stats[s.gameType].confidence += s.analysis.confidence;
      }
    });

    const html = Object.entries(stats).map(([type, data]) => `
      <div class="game-stat-item">
        <span class="game-type-label">${this.formatGameType(type)}</span>
        <div class="stat-details">
          <span class="stat-value">${data.count}</span>
          <span class="stat-label">Total</span>
        </div>
        <div class="stat-details">
          <span class="stat-value">${data.victories}</span>
          <span class="stat-label">Victories</span>
        </div>
        <div class="stat-details">
          <span class="stat-value">${data.defeats}</span>
          <span class="stat-label">Defeats</span>
        </div>
        <div class="stat-details">
          <span class="stat-value">${Math.round(data.confidence / data.count)}%</span>
          <span class="stat-label">Avg Confidence</span>
        </div>
      </div>
    `).join('');
    return html;
  }

  formatGameType(type) {
    const typeNames = {
      'warcraft1': 'Warcraft I',
      'warcraft2': 'Warcraft II',
      'warcraft3': 'Warcraft III',
      'w3champions': 'W3C Champions',
      'battlenet': 'Battle.net'
    };
    return typeNames[type] || type;
  }

  formatMethod(method) {
    const methodNames = {
      'ocr': 'OCR',
      'game-state': 'Game State',
      'player-detection': 'Player Detection',
      'map-detection': 'Map Detection',
      'achievement-detection': 'Achievement Detection'
    };
    return methodNames[method] || method;
  }

  formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }

  getRecentActivityHTML() {
    const activity = [
      { type: 'info', message: 'Screenshot taken: "WC1_Battle_01.png"' },
      { type: 'success', message: 'Screenshot analyzed: "WC1_Battle_01.png" (Victory, 95% confidence)' },
      { type: 'warning', message: 'Auto-reporting enabled for WC1' },
      { type: 'error', message: 'Failed to connect to Electron API' }
    ];

    return activity.map(item => `
      <div class="activity-item ${item.type}">
        <i class="fas fa-${item.type === 'info' ? 'info-circle' : item.type === 'success' ? 'check-circle' : item.type === 'warning' ? 'exclamation-triangle' : 'times-circle'}"></i>
        <span>${item.message}</span>
      </div>
    `).join('');
  }

  formatUptime(seconds) {
    const d = Math.floor(seconds / (3600 * 24));
    const h = Math.floor((seconds % (3600 * 24)) / 3600);
    const m = Math.floor((seconds % 3600) / 60);
    const s = Math.floor(seconds % 60);

    const dDisplay = d > 0 ? d + (d === 1 ? " day, " : " days, ") : "";
    const hDisplay = h > 0 ? h + (h === 1 ? " hour, " : " hours, ") : "";
    const mDisplay = m > 0 ? m + (m === 1 ? " minute, " : " minutes, ") : "";
    const sDisplay = s > 0 ? s + (s === 1 ? " second" : " seconds") : "";
    return dDisplay + hDisplay + mDisplay + sDisplay;
  }

  handleAction(action, element) {
    switch (action) {
      case 'refresh':
        this.loadStatistics();
        this.loadScreenshotHistory();
        this.showNotification('Data refreshed!', 'success');
        break;
      case 'open-folder':
        this.openScreenshotFolder();
        break;
      case 'test-analysis':
        this.testAnalysis();
        break;
      case 'start-monitoring':
        this.toggleAutoAnalysis(true);
        break;
      case 'manual-screenshot':
        this.takeManualScreenshot();
        break;
      case 'report-match':
        this.reportMatch();
        break;
      case 'prev-page':
        this.prevPage();
        break;
      case 'next-page':
        this.nextPage();
        break;
      case 'report-screenshot':
        this.handleReportScreenshot(element.dataset.screenshotId);
        break;
      case 'view-details':
        this.viewScreenshotDetails(element.dataset.screenshotId);
        break;
      case 'reanalyze':
        this.reanalyzeScreenshot(element.dataset.screenshotId);
        break;
    }
  }

  async saveSettings(formData) {
    try {
      const response = await fetch('/api/user-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ screenshots: formData })
      });
      if (response.ok) {
        const data = await response.json();
        this.settings = { ...this.settings, ...data.screenshots };
        this.showNotification('Settings saved!', 'success');
        console.log('⚙️ Settings saved');
      } else {
        const error = await response.json();
        this.showNotification('Failed to save settings: ' + error.message, 'error');
        console.error('❌ Failed to save settings:', error);
      }
    } catch (error) {
      console.error('❌ Failed to save settings:', error);
      this.showNotification('Failed to save settings: ' + error.message, 'error');
    }
  }

  async submitManualReport(formData) {
    try {
      const response = await fetch('/api/manual-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ screenshotId: formData.get('screenshotId') })
      });
      if (response.ok) {
        const data = await response.json();
        this.showNotification('Manual report submitted!', 'success');
        this.loadScreenshotHistory(); // Refresh history to show updated status
        this.loadStatistics(); // Refresh stats to show updated auto-report count
        console.log('📋 Manual report submitted');
        } else {
        const error = await response.json();
        this.showNotification('Failed to submit manual report: ' + error.message, 'error');
        console.error('❌ Failed to submit manual report:', error);
      }
    } catch (error) {
      console.error('❌ Failed to submit manual report:', error);
      this.showNotification('Failed to submit manual report: ' + error.message, 'error');
    }
  }

  async handleRealtimeUpdate(data) {
    console.log('🔄 Real-time update received:', data);
    if (data.type === 'screenshot_analyzed') {
      await this.loadScreenshotHistory(); // Refresh history to show updated status
      await this.loadStatistics(); // Refresh stats to show updated auto-report count
      this.showNotification('Screenshot analyzed!', 'success');
    } else if (data.type === 'match_reported') {
      await this.loadStatistics(); // Refresh stats to show updated auto-report count
      this.showNotification('Match reported!', 'success');
    }
  }

  async handleMatchReported(data) {
    console.log('📋 Match reported received:', data);
    await this.loadStatistics(); // Refresh stats to show updated auto-report count
    this.showNotification('Match reported!', 'success');
  }

  async toggleAutoAnalysis(enabled) {
    try {
      const response = await fetch('/api/user-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ screenshots: { autoAnalysis: enabled } })
      });
      if (response.ok) {
        const data = await response.json();
        this.settings = { ...this.settings, ...data.screenshots };
        this.showNotification(`Auto-analysis ${enabled ? 'enabled' : 'disabled'}`, 'info');
        console.log(`🔍 Auto-analysis ${enabled ? 'enabled' : 'disabled'}`);
      } else {
        const error = await response.json();
        this.showNotification('Failed to update auto-analysis: ' + error.message, 'error');
        console.error('❌ Failed to update auto-analysis:', error);
      }
    } catch (error) {
      console.error('❌ Failed to update auto-analysis:', error);
      this.showNotification('Failed to update auto-analysis: ' + error.message, 'error');
    }
  }

  async openScreenshotFolder() {
    try {
      if (window.electronAPI?.screenshots) {
        await window.electronAPI.screenshots.openFolder();
        this.showNotification('Screenshot folder opened', 'success');
    } else {
        this.showNotification('Feature only available in desktop app', 'info');
      }
    } catch (error) {
      console.error('❌ Failed to open screenshot folder:', error);
      this.showNotification('Failed to open screenshot folder: ' + error.message, 'error');
    }
  }

  async testAnalysis() {
    try {
      if (window.electronAPI?.screenshots) {
        const result = await window.electronAPI.screenshots.testAnalysis();
        this.showNotification(`Test analysis result: ${result.success ? 'Success' : 'Failed'}`, result.success ? 'success' : 'error');
        console.log('🧪 Test analysis result:', result);
      } else {
        this.showNotification('Feature only available in desktop app', 'info');
      }
    } catch (error) {
      console.error('❌ Failed to test analysis:', error);
      this.showNotification('Failed to test analysis: ' + error.message, 'error');
    }
  }

  async takeManualScreenshot() {
    try {
      if (window.electronAPI?.screenshots) {
        const result = await window.electronAPI.screenshots.takeManualScreenshot();
        if (result.success) {
          this.showNotification('Manual screenshot taken!', 'success');
          await this.loadScreenshotHistory(); // Refresh history
          await this.loadStatistics(); // Refresh stats
        } else {
          this.showNotification('Failed to take manual screenshot: ' + result.error, 'error');
        }
      } else {
        this.showNotification('Feature only available in desktop app', 'info');
      }
    } catch (error) {
      console.error('❌ Failed to take manual screenshot:', error);
      this.showNotification('Failed to take manual screenshot: ' + error.message, 'error');
    }
  }

  async reportMatch() {
    try {
      if (window.electronAPI?.matches) {
        const result = await window.electronAPI.matches.reportMatch();
        if (result.success) {
          this.showNotification('Match reported!', 'success');
          await this.loadStatistics(); // Refresh stats
      } else {
          this.showNotification('Failed to report match: ' + result.error, 'error');
        }
      } else {
        this.showNotification('Feature only available in desktop app', 'info');
      }
    } catch (error) {
      console.error('❌ Failed to report match:', error);
      this.showNotification('Failed to report match: ' + error.message, 'error');
    }
  }

  async prevPage() {
    // Implement pagination logic here
    console.log('Previous page clicked');
  }

  async nextPage() {
    // Implement pagination logic here
    console.log('Next page clicked');
  }

  async handleReportScreenshot(screenshotId) {
    try {
      const screenshot = this.screenshots.find(s => s.id === screenshotId);
      if (!screenshot || !screenshot.analysis?.result) {
        this.showNotification('Screenshot cannot be reported', 'error');
        return;
      }

      if (window.electronAPI?.screenshots) {
        const result = await window.electronAPI.screenshots.reportResult(screenshotId);
        if (result.success) {
          // Update screenshot as reported
          screenshot.reportedToLadder = true;
          this.displayRecentScreenshots();
          this.showNotification('Screenshot result reported to ladder', 'success');
          await this.loadStatistics(); // Refresh stats to show updated auto-report count
      } else {
          this.showNotification('Failed to report: ' + result.error, 'error');
        }
      } else {
        this.showNotification('Reporting requires desktop app', 'info');
      }
    } catch (error) {
      console.error('❌ Failed to report screenshot:', error);
      this.showNotification('Failed to report screenshot', 'error');
    }
  }

  async viewScreenshotDetails(screenshotId) {
    const screenshot = this.screenshots.find(s => s.id === screenshotId);
    if (screenshot) {
      const modal = document.createElement('div');
      modal.className = 'modal';
      modal.innerHTML = `
        <div class="modal-content">
          <span class="close-button" onclick="this.parentElement.remove()">&times;</span>
          <h2>Screenshot Details</h2>
          <p><strong>ID:</strong> ${screenshot.id}</p>
          <p><strong>Game Type:</strong> ${this.formatGameType(screenshot.gameType)}</p>
          <p><strong>Result:</strong> ${screenshot.result.toUpperCase()}</p>
          <p><strong>Confidence:</strong> ${Math.round(screenshot.confidence)}%</p>
          <p><strong>Timestamp:</strong> ${this.formatTimestamp(screenshot.createdAt)}</p>
          <p><strong>Path:</strong> ${screenshot.imagePath}</p>

          ${screenshot.players && screenshot.players.length > 0 ? `
            <h3>Players</h3>
            <ul>
              ${screenshot.players.map(player => `<li>${player.name}</li>`).join('')}
            </ul>
          ` : ''}

          ${screenshot.mapName ? `
            <h3>Map</h3>
            <p>${screenshot.mapName}</p>
          ` : ''}

          ${screenshot.duration ? `
            <h3>Duration</h3>
            <p>${screenshot.duration}</p>
          ` : ''}

          ${screenshot.analysis && screenshot.analysis.methods ? `
            <h3>Analysis Methods</h3>
            <ul>
              ${screenshot.analysis.methods.map(method => `<li>${this.formatMethod(method)}</li>`).join('')}
            </ul>
          ` : ''}

          ${screenshot.achievements && screenshot.achievements.length > 0 ? `
            <h3>Achievements</h3>
            <ul>
              ${screenshot.achievements.map(achievement => `<li>${achievement}</li>`).join('')}
            </ul>
          ` : ''}

          ${screenshot.analysis && screenshot.analysis.result ? `
            <h3>Analysis Result</h3>
            <p>${screenshot.analysis.result} (${screenshot.analysis.confidence}%)</p>
          ` : ''}

          ${screenshot.reportedToLadder ? `
            <h3>Report Status</h3>
            <p>Reported to ladder: ✅</p>
          ` : ''}
        </div>
      `;
      document.body.appendChild(modal);
    }
  }

  async reanalyzeScreenshot(screenshotId) {
    try {
      if (window.electronAPI?.screenshots) {
        const result = await window.electronAPI.screenshots.reanalyze(screenshotId);
        if (result.success) {
          this.showNotification('Screenshot re-analyzed!', 'success');
          await this.loadScreenshotHistory(); // Refresh history to show updated status
          await this.loadStatistics(); // Refresh stats to show updated auto-report count
        } else {
          this.showNotification('Failed to re-analyze: ' + result.error, 'error');
        }
      } else {
        this.showNotification('Feature only available in desktop app', 'info');
      }
    } catch (error) {
      console.error('❌ Failed to re-analyze screenshot:', error);
      this.showNotification('Failed to re-analyze screenshot: ' + error.message, 'error');
    }
  }

  /**
   * Get mock games for demo purposes
   */
  getMockGames() {
    return [
      {
        id: 1,
        name: 'Warcraft II: Tides of Darkness',
        path: 'C:\\Program Files\\Warcraft II\\Warcraft II BNE.exe',
        running: false,
        lastPlayed: new Date(Date.now() - 3600000) // 1 hour ago
      },
      {
        id: 2,
        name: 'Warcraft I: Orcs & Humans',
        path: 'C:\\Program Files\\Warcraft\\WAR.EXE',
        running: false,
        lastPlayed: new Date(Date.now() - 86400000) // 1 day ago
      }
    ];
  }

  /**
   * Show notification message
   */
  showNotification(message, type = 'info') {
    // Try to use existing notification system
    if (window.showNotification) {
      window.showNotification(message, type);
      return;
    }
    
    // Fallback notification
    const notification = document.createElement('div');
    notification.className = `game-manager-notification ${type}`;
    notification.innerHTML = `
      <i class="fas fa-info-circle"></i>
      <span>${message}</span>
    `;
    
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'error' ? 'rgba(220, 53, 69, 0.9)' : type === 'success' ? 'rgba(40, 167, 69, 0.9)' : 'rgba(74, 144, 226, 0.9)'};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      z-index: 10000;
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      animation: slideInNotification 0.3s ease;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      notification.style.animation = 'slideOutNotification 0.3s ease';
      setTimeout(() => notification.remove(), 300);
    }, 3000);
  }

  getSettingsHTML() {
    return `
      <div class="settings-view">
        <form id="screenshot-settings-form" class="settings-form">
          
          <!-- Analysis Settings -->
          <div class="settings-section">
            <h3>🔍 Analysis Settings</h3>
            
            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" ${this.settings.autoAnalysis ? 'checked' : ''} name="autoAnalysis">
                <span class="checkmark"></span>
                Auto-analyze screenshots
              </label>
              <p class="setting-description">Automatically analyze new screenshots as they're detected</p>
        </div>

            <div class="setting-item">
              <label class="setting-label">Confidence Threshold</label>
              <div class="slider-container">
                <input type="range" name="confidenceThreshold" 
                       min="30" max="95" value="${this.settings.confidenceThreshold}"
                       class="setting-slider" id="confidence-slider">
                <span class="slider-value">${this.settings.confidenceThreshold}%</span>
        </div>
              <p class="setting-description">Minimum confidence required for processing results</p>
        </div>

      </div>

          <!-- Reporting Settings -->
          <div class="settings-section">
            <h3>📊 Reporting Settings</h3>
            
            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" ${this.settings.autoReport ? 'checked' : ''} name="autoReport">
                <span class="checkmark"></span>
                Auto-report to ladder
              </label>
              <p class="setting-description">Automatically submit high-confidence results to the ladder</p>
            </div>

          </div>

          <!-- Game Types -->
          <div class="settings-section">
            <h3>🎮 Game Types</h3>
            
            <div class="game-types-grid">
              <label class="game-type-label">
                <input type="checkbox" ${this.settings.gameTypes.includes('warcraft1') ? 'checked' : ''} 
                       name="gameTypes" value="warcraft1">
                <span class="game-type-card">
                  <span class="game-icon">⚔️</span>
                  <span class="game-name">Warcraft I</span>
                </span>
              </label>
              
              <label class="game-type-label">
                <input type="checkbox" ${this.settings.gameTypes.includes('warcraft2') ? 'checked' : ''} 
                       name="gameTypes" value="warcraft2">
                <span class="game-type-card">
                  <span class="game-icon">🏰</span>
                  <span class="game-name">Warcraft II</span>
                </span>
              </label>
              
              <label class="game-type-label">
                <input type="checkbox" ${this.settings.gameTypes.includes('warcraft3') ? 'checked' : ''} 
                       name="gameTypes" value="warcraft3">
                <span class="game-type-card">
                  <span class="game-icon">🌟</span>
                  <span class="game-name">Warcraft III</span>
                </span>
              </label>
            </div>

          </div>

          <!-- Notification Settings -->
          <div class="settings-section">
            <h3>🔔 Notifications</h3>
            
            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" ${this.settings.notifications ? 'checked' : ''} name="notifications">
                <span class="checkmark"></span>
                Desktop notifications
              </label>
              <p class="setting-description">Show notifications when matches are detected</p>
        </div>
    
        </div>

          <!-- Save Button -->
          <div class="settings-actions">
            <button type="submit" class="action-btn primary large">
              💾 Save Settings
            </button>
            <button type="button" class="action-btn secondary" data-action="reset-settings">
              🔄 Reset to Defaults
            </button>
      </div>

        </form>
        </div>
      `;
  }

  getHistoryHTML() {
    return `
      <div class="history-view">
        
        <!-- Match History Stats -->
        <div class="history-stats">
          <div class="stat-card">
            <h4>Recent Matches</h4>
            <div class="stat-number">${this.screenshots.length}</div>
        </div>
          <div class="stat-card">
            <h4>Win Rate</h4>
            <div class="stat-number">${this.calculateWinRate()}%</div>
        </div>
          <div class="stat-card">
            <h4>Best Streak</h4>
            <div class="stat-number">${this.calculateBestStreak()}</div>
      </div>
          <div class="stat-card">
            <h4>Auto-Reports</h4>
            <div class="stat-number">${this.stats.autoReported || 0}</div>
          </div>
        </div>

        <!-- Timeline View -->
        <div class="history-timeline">
          <h3>📅 Match Timeline</h3>
          <div class="timeline-container">
            ${this.getTimelineHTML()}
          </div>
        </div>

        <!-- Manual Report Form -->
        <div class="manual-report-section">
          <h3>📋 Manual Report</h3>
          <form id="manual-report-form" class="manual-report-form">
            <div class="form-grid">
              <div class="form-group">
                <label>Opponent Name</label>
                <input type="text" name="opponent" required placeholder="Enter opponent username">
              </div>
              <div class="form-group">
                <label>Result</label>
                <select name="result" required>
                  <option value="">Select result</option>
                  <option value="victory">Victory 🏆</option>
                  <option value="defeat">Defeat 💔</option>
                </select>
              </div>
              <div class="form-group">
                <label>Game Type</label>
                <select name="gameType" required>
                  <option value="warcraft3">Warcraft III</option>
                  <option value="warcraft2">Warcraft II</option>
                  <option value="warcraft1">Warcraft I</option>
                </select>
              </div>
              <div class="form-group">
                <label>Map Name (Optional)</label>
                <input type="text" name="mapName" placeholder="e.g. Lost Temple">
              </div>
            </div>
            <button type="submit" class="action-btn primary">Submit Report</button>
          </form>
        </div>

      </div>
    `;
  }

  // Action Handlers
  async handleAction(action, element) {
    console.log(`🎯 Handling action: ${action}`);

    switch (action) {
      case 'refresh':
        await this.refreshData();
        break;
      case 'start-monitoring':
        await this.startMonitoring();
        break;
      case 'stop-monitoring':
        await this.stopMonitoring();
        break;
      case 'manual-screenshot':
        await this.takeManualScreenshot();
        break;
      case 'open-folder':
        await this.openScreenshotFolder();
        break;
      case 'test-analysis':
        await this.testAnalysis();
        break;
      case 'report-screenshot':
        await this.reportScreenshot(element.dataset.screenshotId);
        break;
      case 'view-details':
        this.viewScreenshotDetails(element.dataset.screenshotId);
        break;
      case 'reanalyze':
        await this.reanalyzeScreenshot(element.dataset.screenshotId);
        break;
      case 'reset-settings':
        this.resetSettings();
        break;
      default:
        console.log(`❓ Unknown action: ${action}`);
    }
  }

  async refreshData() {
    console.log('🔄 Refreshing all data...');
    await Promise.all([
      this.loadScreenshotHistory(),
      this.loadStatistics(),
      this.loadUserSettings()
    ]);
    this.renderInterface();
    this.showNotification('✅ Data refreshed successfully', 'success');
  }

  async startMonitoring() {
    if (window.electronAPI) {
      try {
        await window.electronAPI.screenshots.startMonitoring();
        this.showNotification('🔍 Screenshot monitoring started', 'success');
        await this.refreshData();
      } catch (error) {
        this.showNotification('❌ Failed to start monitoring', 'error');
        }
      } else {
      this.showNotification('⚠️ Desktop app required for monitoring', 'warning');
    }
  }

  async stopMonitoring() {
    if (window.electronAPI) {
      try {
        await window.electronAPI.screenshots.stopMonitoring();
        this.showNotification('🛑 Screenshot monitoring stopped', 'info');
        await this.refreshData();
    } catch (error) {
        this.showNotification('❌ Failed to stop monitoring', 'error');
      }
    }
  }

  async takeManualScreenshot() {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.screenshots.captureManual();
        if (result.success) {
          this.showNotification('📸 Screenshot captured and queued for analysis', 'success');
          setTimeout(() => this.refreshData(), 2000); // Refresh after analysis
        } else {
          this.showNotification('❌ Failed to capture screenshot', 'error');
      }
    } catch (error) {
        this.showNotification('❌ Screenshot capture failed', 'error');
      }
    } else {
      this.showNotification('⚠️ Desktop app required for manual screenshots', 'warning');
    }
  }

  async openScreenshotFolder() {
    if (window.electronAPI) {
      try {
        await window.electronAPI.screenshots.openFolder();
    } catch (error) {
        console.error('❌ Error opening folder:', error);
      }
    } else {
      this.showNotification('⚠️ Desktop app required', 'warning');
    }
  }

  async saveSettings(formData) {
    try {
      const newSettings = {
        autoAnalysis: formData.has('autoAnalysis'),
        autoReport: formData.has('autoReport'),
        confidenceThreshold: parseInt(formData.get('confidenceThreshold')),
        gameTypes: formData.getAll('gameTypes'),
        notifications: formData.has('notifications')
      };

      // Update local settings
      this.settings = { ...this.settings, ...newSettings };

      // Save to backend
      const response = await fetch('/api/screenshot-settings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSettings)
      });

      if (response.ok) {
        // Update Electron app settings if available
        if (window.electronAPI) {
          await window.electronAPI.screenshots.updateSettings(newSettings);
        }

        this.showNotification('⚙️ Settings saved successfully', 'success');
        this.renderInterface(); // Re-render to show updated status
      } else {
        throw new Error('Failed to save settings');
      }

    } catch (error) {
      console.error('❌ Error saving settings:', error);
      this.showNotification('❌ Failed to save settings', 'error');
    }
  }

  // Utility Functions
  formatGameType(gameType) {
    const gameNames = {
      warcraft1: 'Warcraft I',
      warcraft2: 'Warcraft II',
      warcraft3: 'Warcraft III'
    };
    return gameNames[gameType] || gameType;
  }

  formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString();
  }

  formatUptime(seconds) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }

  formatMethod(method) {
    const methodNames = {
      'comprehensive_ocr': 'OCR',
      'text_result_detection': 'Text',
      'player_detection': 'Players',
      'color_analysis': 'Colors',
      'advanced_heuristics': 'Heuristics'
    };
    return methodNames[method] || method;
  }

  calculateWinRate() {
    const victories = this.screenshots.filter(s => s.result === 'victory').length;
    const total = this.screenshots.filter(s => ['victory', 'defeat'].includes(s.result)).length;
    return total > 0 ? Math.round((victories / total) * 100) : 0;
  }

  calculateBestStreak() {
    let currentStreak = 0;
    let bestStreak = 0;
    
    for (const screenshot of this.screenshots.slice().reverse()) {
      if (screenshot.result === 'victory') {
        currentStreak++;
        bestStreak = Math.max(bestStreak, currentStreak);
      } else {
        currentStreak = 0;
      }
    }
    
    return bestStreak;
  }

  getRecentActivityHTML() {
    const recentScreenshots = this.screenshots.slice(0, 5);
    if (recentScreenshots.length === 0) {
      return '<div class="no-activity">No recent activity</div>';
    }

    return recentScreenshots.map(screenshot => `
      <div class="activity-item">
        <span class="activity-icon">${screenshot.result === 'victory' ? '🏆' : '💔'}</span>
        <div class="activity-details">
          <div class="activity-title">${screenshot.result.toUpperCase()}</div>
          <div class="activity-subtitle">${this.formatGameType(screenshot.gameType)} • ${this.formatTimestamp(screenshot.createdAt)}</div>
        </div>
        <span class="activity-confidence">${Math.round(screenshot.confidence)}%</span>
      </div>
    `).join('');
  }

  getGameStatsHTML() {
    const gameStats = {
      warcraft1: { victories: 0, defeats: 0 },
      warcraft2: { victories: 0, defeats: 0 },
      warcraft3: { victories: 0, defeats: 0 }
    };

    this.screenshots.forEach(screenshot => {
      if (gameStats[screenshot.gameType]) {
        if (screenshot.result === 'victory') {
          gameStats[screenshot.gameType].victories++;
        } else if (screenshot.result === 'defeat') {
          gameStats[screenshot.gameType].defeats++;
        }
      }
    });

    return Object.entries(gameStats).map(([gameType, stats]) => {
      const total = stats.victories + stats.defeats;
      const winRate = total > 0 ? Math.round((stats.victories / total) * 100) : 0;
      
      return `
        <div class="game-stat-item">
          <div class="game-stat-header">
            <span class="game-stat-name">${this.formatGameType(gameType)}</span>
            <span class="game-stat-winrate">${winRate}%</span>
          </div>
          <div class="game-stat-details">
            <span class="victories">${stats.victories}W</span>
            <span class="defeats">${stats.defeats}L</span>
          </div>
        </div>
      `;
    }).join('');
  }

  getTimelineHTML() {
    return this.screenshots.slice(0, 10).map(screenshot => `
      <div class="timeline-item ${screenshot.result}">
        <div class="timeline-marker"></div>
        <div class="timeline-content">
          <div class="timeline-header">
            <span class="timeline-result">${screenshot.result === 'victory' ? '🏆' : '💔'} ${screenshot.result.toUpperCase()}</span>
            <span class="timeline-time">${this.formatTimestamp(screenshot.createdAt)}</span>
          </div>
          <div class="timeline-details">
            <span class="timeline-game">${this.formatGameType(screenshot.gameType)}</span>
            ${screenshot.mapName ? `<span class="timeline-map">• ${screenshot.mapName}</span>` : ''}
            <span class="timeline-confidence">• ${Math.round(screenshot.confidence)}% confidence</span>
          </div>
        </div>
      </div>
    `).join('');
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
      notification.remove();
    }, 3000);
  }

  handleRealtimeUpdate(data) {
    console.log('📡 Received real-time update:', data);
    // Update UI with real-time data
    this.refreshData();
  }

  startAutoRefresh() {
    // Don't start intervals in Electron mode to prevent infinite loops
    if (window.isElectronMode) {
      console.log('🖥️ Electron mode detected - skipping auto-refresh in GameManagerPage');
      return;
    }
    
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
    
    // Refresh every 30 seconds
    this.refreshInterval = setInterval(() => {
      this.loadStatistics();
    }, 30000);
  }

  destroy() {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }
    this.initialized = false;
  }
}

// Initialize when DOM is ready
if (typeof window !== 'undefined') {
  window.GameManagerPage = GameManagerPage;
  
  // Auto-initialize if container exists
  document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('game-manager-content')) {
      window.gameManagerPage = new GameManagerPage();
      window.gameManagerPage.init();
    }
  });
} 