/* ==========================================================================
   WARCRAFT ARENA - CONSOLIDATED LADDER STYLES
   Clean, consolidated styles for the ladder page
   ========================================================================== */

/* ==========================================================================
   LAYOUT STRUCTURE
   ========================================================================== */

/* Hide element utility */
.hidden {
  display: none;
}

/* Header Section */
.ladder-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: var(--spacing-lg) 0;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.game-toggle-section {
  flex: 0 0 auto;
}

.report-match-section {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* Game Toggle Button */
.game-toggle-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 12px 16px;
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 280px;
}

.game-toggle-btn:hover {
  background: linear-gradient(135deg, var(--bg-hover), var(--bg-secondary));
  border-color: var(--primary-gold);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.game-toggle-btn .game-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--primary-gold);
  border-radius: 50%;
  color: var(--text-dark);
  font-size: 18px;
}

.game-toggle-btn .game-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.game-toggle-btn .arena-title {
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.game-toggle-btn .current-game {
  font-weight: 700;
  font-size: 1rem;
  color: var(--text-primary);
  margin: 2px 0;
}

.game-toggle-btn .switch-text {
  font-size: 0.75rem;
  color: var(--text-muted);
  opacity: 0.8;
}

.game-toggle-btn .toggle-indicator {
  color: var(--primary-gold);
  font-size: 16px;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.game-toggle-btn:hover .toggle-indicator {
  opacity: 1;
  transform: scale(1.1);
}

/* Report Match Button */
.btn-report {
  background: linear-gradient(135deg, var(--primary-gold), var(--primary-gold-dark));
  border: 2px solid var(--primary-gold-dark);
  color: var(--text-dark);
  font-weight: 700;
  padding: 12px 24px;
  border-radius: var(--radius-lg);
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-report::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.btn-report:hover {
  background: linear-gradient(135deg, var(--primary-gold-dark), var(--primary-gold));
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(212, 175, 55, 0.6);
}

.btn-report:hover::before {
  left: 100%;
}

/* ==========================================================================
   SEARCH AND FILTERS SECTION - Enhanced Styling
   ========================================================================== */

.controls-section {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
}

.player-search {
  margin-bottom: var(--spacing-lg);
}

.search-input-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--bg-tertiary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xs);
  transition: all 0.3s ease;
  width: 100%;
  max-width: 500px;
  min-width: 200px;
  overflow: hidden;
  position: relative;
}

.search-input-group:focus-within {
  border-color: var(--primary-gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}

#player-search {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1rem;
  padding: var(--spacing-md);
  outline: none;
  font-family: 'Cinzel', serif;
}

#player-search::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Enhanced Search Button */
.btn-search {
  background: linear-gradient(135deg, var(--primary-gold) 0%, #b8860b 100%);
  color: var(--bg-primary);
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  font-weight: 600;
  font-family: 'Cinzel', serif;
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
}

.btn-search:hover {
  background: linear-gradient(135deg, #b8860b 0%, var(--primary-gold) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.4);
}

.btn-search:active {
  transform: translateY(0);
}

.btn-search i {
  font-size: 0.9rem;
}

/* Clear Button - Override with consistent styling */
.btn-clear {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 2px solid var(--border-primary);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  display: none;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  flex-shrink: 0;
}

.btn-clear:hover {
  background: var(--primary-gold);
  color: var(--bg-primary);
  border-color: var(--primary-gold);
  transform: translateY(-2px);
}

.btn-clear:active {
  transform: translateY(0);
}

.btn-clear i {
  font-size: 0.9rem;
}

/* Match Type Filter Styling */
.match-type-filter {
  border-top: 1px solid var(--border-primary);
  padding-top: var(--spacing-lg);
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
}

.filter-btn {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 2px solid var(--border-primary);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-family: 'Cinzel', serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
  min-width: 60px;
}

.filter-btn:hover {
  background: var(--primary-gold);
  color: var(--bg-primary);
  border-color: var(--primary-gold);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.filter-btn.active {
  background: linear-gradient(135deg, var(--primary-gold) 0%, #b8860b 100%);
  color: var(--bg-primary);
  border-color: var(--primary-gold);
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
}

.filter-btn.active:hover {
  background: linear-gradient(135deg, #b8860b 0%, var(--primary-gold) 100%);
}

/* Responsive Design - Fix overlapping */
@media (max-width: 900px) {
  .search-input-group {
    min-width: 180px;
  }
  
  .btn-search span {
    display: none;
  }
  
  .btn-search {
    min-width: 40px;
    padding: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .search-input-group {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
    min-width: auto;
  }
  
  .btn-search {
    justify-content: center;
  }
  
  .btn-search span {
    display: inline;
  }
  
  .filter-buttons {
    grid-template-columns: repeat(3, 1fr);
    display: grid;
  }
  
  .filter-btn {
    font-size: 0.8rem;
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .controls-section {
    padding: var(--spacing-md);
  }
  
  .search-input-group {
    max-width: 100%;
  }
  
  .filter-buttons {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xs);
  }
}

/* ==========================================================================
   MAIN LAYOUT
   ========================================================================== */

.ladder-layout {
  display: grid;
  grid-template-columns: 1fr 300px 250px;
  grid-template-areas: "leaderboard ranks matches";
  gap: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
}

.leaderboard-section {
  grid-area: leaderboard;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
}

.ranks-sidebar {
  grid-area: ranks;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
  height: 700px; /* Fixed height to match leaderboard with 10 players */
  display: flex;
  flex-direction: column;
}

.recent-matches-sidebar {
  grid-area: matches;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
  height: 700px; /* Fixed height to match leaderboard with 10 players */
  display: flex;
  flex-direction: column;
}

/* ==========================================================================
   SIDEBAR HEADERS
   ========================================================================== */

.ranks-sidebar h3,
.recent-matches-sidebar h3 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--primary-gold);
  text-align: center;
  font-family: 'Cinzel', serif;
  flex-shrink: 0; /* Prevent header from shrinking */
}

/* ==========================================================================
   SCROLLABLE CONTAINERS
   ========================================================================== */

.ranks-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  flex: 1; /* Take up remaining space */
  overflow-y: auto; /* Enable vertical scrolling */
  padding-right: var(--spacing-xs); /* Space for scrollbar */
}

.recent-matches-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  flex: 1; /* Take up remaining space */
  overflow-y: auto; /* Enable vertical scrolling */
  padding-right: var(--spacing-xs); /* Space for scrollbar */
}

/* ==========================================================================
   CUSTOM SCROLLBARS
   ========================================================================== */

.ranks-container::-webkit-scrollbar,
.recent-matches-container::-webkit-scrollbar {
  width: 6px;
}

.ranks-container::-webkit-scrollbar-track,
.recent-matches-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.ranks-container::-webkit-scrollbar-thumb,
.recent-matches-container::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.ranks-container::-webkit-scrollbar-thumb:hover,
.recent-matches-container::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.5);
}

/* Firefox scrollbar styling */
.ranks-container,
.recent-matches-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(212, 175, 55, 0.3) rgba(255, 255, 255, 0.05);
}

/* ==========================================================================
   RESPONSIVE LAYOUT
   ========================================================================== */

/* Responsive Layout */
@media (max-width: 1400px) {
  .ladder-layout {
    grid-template-columns: 1fr 250px;
    grid-template-areas: 
      "leaderboard ranks"
      "matches matches";
  }
  
  .ranks-sidebar {
    height: 500px; /* Smaller height for medium screens */
  }
  
  .recent-matches-sidebar {
    height: 400px; /* Smaller height for medium screens */
  }
}

@media (max-width: 900px) {
  .ladder-layout {
    grid-template-columns: 1fr;
    grid-template-areas: 
      "leaderboard"
      "ranks"
      "matches";
  }
  
  .ranks-sidebar,
  .recent-matches-sidebar {
    width: 100%;
    height: 400px; /* Consistent height for mobile */
  }
}

@media (max-width: 600px) {
  .controls-section {
    padding: 0 var(--spacing-sm);
  }
  
  .player-search {
    flex-direction: column;
    width: 100%;
  }
  
  .player-search input {
    width: 100%;
  }
  
  .ladder-header {
    flex-direction: column;
    text-align: center;
  }
  
  .game-toggle-btn {
    min-width: auto;
    width: 100%;
  }
  
  .ranks-sidebar,
  .recent-matches-sidebar {
    height: 350px; /* Smaller height for small screens */
  }
}

/* ==========================================================================
   LEADERBOARD
   ========================================================================== */

.leaderboard-section h2 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--primary-gold);
  text-align: center;
  font-family: 'Cinzel', serif;
}

.leaderboard-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--spacing-lg);
}

.leaderboard-table th,
.leaderboard-table th.rank-header,
.leaderboard-table th.player-header,
.leaderboard-table th.mmr-header,
.leaderboard-table th.wins-header,
.leaderboard-table th.losses-header,
.leaderboard-table th.ratio-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  color: var(--text-primary);
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 2px solid var(--border-primary);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
  border-left: none;
  border-right: none;
  border-top: none;
}

.leaderboard-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
  color: var(--text-primary);
}

.leaderboard-table tbody tr {
  transition: all 0.2s ease;
}

.leaderboard-table tbody tr:hover {
  background: var(--bg-hover);
  transform: translateX(2px);
}

.player-rank {
  text-align: center;
  position: relative;
}

.player-rank-image {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

/* Rank images in leaderboard table */
.leaderboard-table .rank-image {
  width: 48px;
  height: 48px;
  object-fit: contain;
  border-radius: 6px;
}

/* Rank badge container styling */
.leaderboard-table .rank-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: rgba(212, 175, 55, 0.1);
  border: 1px solid rgba(212, 175, 55, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.leaderboard-table .rank-badge:hover {
  background: rgba(212, 175, 55, 0.2);
  border-color: rgba(212, 175, 55, 0.4);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

.leaderboard-table .rank-badge img {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.leaderboard-table .rank-badge:hover img {
  filter: drop-shadow(0 0 8px rgba(212, 175, 55, 0.6));
}

.leaderboard-table .rank-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.leaderboard-table .rank-number {
  font-weight: 700;
  font-size: 1.1rem;
  color: var(--text-primary);
  min-width: 30px;
  text-align: center;
}

/* Rank-specific glow effects */
.leaderboard-table .rank-badge:hover img[src*="champion"] {
  filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8));
}

.leaderboard-table .rank-badge:hover img[src*="/s"] {
  filter: drop-shadow(0 0 12px rgba(65, 105, 225, 0.8));
}

.leaderboard-table .rank-badge:hover img[src*="/a"] {
  filter: drop-shadow(0 0 12px rgba(255, 191, 0, 0.8));
}

.leaderboard-table .rank-badge:hover img[src*="/g"] {
  filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8));
}

.leaderboard-table .rank-badge:hover img[src*="/b"] {
  filter: drop-shadow(0 0 12px rgba(205, 127, 50, 0.8));
}

/* MMR value styling */
.leaderboard-table .mmr-value {
  font-size: 0.75em; /* 25% smaller than default */
  font-weight: 600;
}

/* Alternating row background colors */
.leaderboard-table tbody .player-row:nth-child(odd) {
  background-color: var(--bg-secondary);
}

.leaderboard-table tbody .player-row:nth-child(even) {
  background-color: rgba(139, 69, 19, 0.15); /* Subtle reddish-brown tint */
}

.leaderboard-table tbody .player-row:hover {
  background-color: rgba(218, 165, 32, 0.15); /* Amber hover for both */
}

.player-name {
  font-weight: 600;
  color: var(--text-primary);
  cursor: pointer;
  transition: color 0.2s ease;
}

.player-name:hover {
  color: var(--primary-gold);
}

.player-mmr {
  font-weight: 700;
  color: var(--primary-gold);
}

.player-ratio {
  font-weight: 600;
}

.player-ratio.excellent { color: #4CAF50; }
.player-ratio.good { color: #8BC34A; }
.player-ratio.average { color: #FFC107; }
.player-ratio.poor { color: #F44336; }

.loading-cell {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-xl);
}

/* ==========================================================================
   PAGINATION
   ========================================================================== */

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.pagination-controls button {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
}

.pagination-controls button:hover:not(:disabled) {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

.pagination-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* ==========================================================================
   RANKS SIDEBAR
   ========================================================================== */

.loading {
  text-align: center;
  color: var(--text-secondary);
  font-style: italic;
  padding: var(--spacing-lg);
}

.rank-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
  transition: all 0.3s ease;
  margin-bottom: var(--spacing-md);
  flex-shrink: 0; /* Prevent items from shrinking in scroll container */
}

.rank-item:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

/* ==========================================================================
   RANK IMAGE GLOW EFFECTS
   ========================================================================== */

.ladder-layout .ranks-sidebar .ranks-container .rank-item .rank-image,
.ladder-layout .ranks-sidebar .rank-item img[src*="/ranks/"],
.ranks-sidebar .ranks-container .rank-item img,
.ranks-sidebar .rank-item .rank-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  max-width: 60px;
  max-height: 60px;
  transition: all 0.3s ease;
  border-radius: var(--radius-sm);
  filter: drop-shadow(0 0 5px rgba(212, 175, 55, 0.1));
}

/* Rank Image Hover Glow Effects */
.ranks-sidebar .rank-item:hover .rank-image,
.ranks-sidebar .rank-item:hover img {
  filter: drop-shadow(0 0 20px rgba(212, 175, 55, 0.6)) 
          drop-shadow(0 0 40px rgba(212, 175, 55, 0.4))
          drop-shadow(0 0 60px rgba(212, 175, 55, 0.2));
  transform: scale(1.05);
}

/* Specific glow colors for different rank tiers */
.rank-item:hover img[src*="champion"] {
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)) 
          drop-shadow(0 0 40px rgba(255, 215, 0, 0.6))
          drop-shadow(0 0 60px rgba(255, 215, 0, 0.4));
}

.rank-item:hover img[src*="/s"] { /* Sapphire ranks */
  filter: drop-shadow(0 0 20px rgba(65, 105, 225, 0.8)) 
          drop-shadow(0 0 40px rgba(65, 105, 225, 0.6))
          drop-shadow(0 0 60px rgba(65, 105, 225, 0.4));
}

.rank-item:hover img[src*="/a"] { /* Amber ranks */
  filter: drop-shadow(0 0 20px rgba(255, 191, 0, 0.8)) 
          drop-shadow(0 0 40px rgba(255, 191, 0, 0.6))
          drop-shadow(0 0 60px rgba(255, 191, 0, 0.4));
}

.rank-item:hover img[src*="/g"] { /* Gold ranks */
  filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)) 
          drop-shadow(0 0 40px rgba(255, 215, 0, 0.6))
          drop-shadow(0 0 60px rgba(255, 215, 0, 0.4));
}

.rank-item:hover img[src*="/b"] { /* Bronze ranks */
  filter: drop-shadow(0 0 20px rgba(205, 127, 50, 0.8)) 
          drop-shadow(0 0 40px rgba(205, 127, 50, 0.6))
          drop-shadow(0 0 60px rgba(205, 127, 50, 0.4));
}

.rank-details {
  flex: 1;
}

.rank-name {
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 2px 0;
  transition: color 0.3s ease;
}

.rank-item:hover .rank-name {
  color: var(--primary-gold);
  text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
}

.rank-threshold {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin: 0;
  transition: color 0.3s ease;
}

.rank-item:hover .rank-threshold {
  color: var(--text-primary);
}

/* ==========================================================================
   RECENT MATCHES SIDEBAR
   ========================================================================== */

.match-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-secondary);
  transition: all 0.2s ease;
}

.match-card:hover {
  background: var(--bg-hover);
  border-color: var(--border-hover);
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.match-type {
  font-weight: 600;
  color: var(--primary-gold);
  font-size: 0.9rem;
}

.match-date {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.match-details {
  margin-bottom: var(--spacing-xs);
}

.match-map,
.match-resource {
  display: block;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.match-players {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.player-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
}

.player-result .player-name {
  color: var(--text-primary);
}

.player-mmr-change {
  font-weight: 600;
}

.player-mmr-change.win {
  color: #4CAF50;
}

.player-mmr-change.loss {
  color: #F44336;
}

/* ==========================================================================
   STATISTICS SECTION
   ========================================================================== */

.stats-section {
  margin: var(--spacing-xl) 0;
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
}

.stats-section h2 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--primary-gold);
  text-align: center;
  font-family: 'Cinzel', serif;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-lg);
}

.stats-card {
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-secondary);
  min-height: 280px;
}

.stats-card h3 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--primary-gold);
  text-align: center;
  font-size: 1.1rem;
}

.stats-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  align-items: start;
  height: calc(100% - 2rem);
}

/* Chart-only layout for pure chart cards */
.stats-chart-only {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 220px;
  width: 100%;
}

.stats-chart-only canvas {
  max-width: 100%;
  max-height: 100%;
}

.stats-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.stats-list li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
  border-bottom: 1px solid var(--border-tertiary);
}

.stats-list li:last-child {
  border-bottom: none;
}

.stats-name {
  color: var(--text-primary);
  font-weight: 500;
}

.stats-value {
  color: var(--primary-gold);
  font-weight: 600;
}

.stats-chart {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.stats-chart canvas {
  max-width: 100%;
  max-height: 100%;
}

/* Overview section styling */
#overview-stats {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(255, 215, 0, 0.05) 100%);
}

#overview-stats .stats-content {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

#overview-stats .stats-list {
  display: contents;
}

#overview-stats .stats-list ul {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
  padding: var(--spacing-sm);
  border: 1px solid var(--border-secondary);
}

@media (max-width: 1200px) {
  .stats-container {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
  
  .stats-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .stats-chart,
  .stats-chart-only {
    height: 180px;
  }
  
  .stats-card {
    min-height: 250px;
  }
}

@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }
  
  .stats-chart,
  .stats-chart-only {
    height: 160px;
  }
  
  .stats-card {
    min-height: 220px;
    padding: var(--spacing-md);
  }
  
  #overview-stats .stats-content {
    grid-template-columns: 1fr;
  }
}

/* ==========================================================================
   MODAL STYLES
   ========================================================================== */

.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal.show {
  display: flex;
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  border: 2px solid var(--border-primary);
  box-shadow: var(--shadow-lg);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
}

.modal-header h2,
.modal-header h3 {
  margin: 0;
  color: var(--primary-gold);
  font-family: 'Cinzel', serif;
}

.close-modal {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.close-modal:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

/* ==========================================================================
   FORM STYLES
   ========================================================================== */

.form-section {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.form-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--primary-gold);
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  color: var(--text-primary);
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--border-active);
  box-shadow: 0 0 0 2px rgba(136, 192, 208, 0.2);
}

.resource-buttons {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.resource-buttons input[type="radio"] {
  display: none;
}

.resource-buttons label {
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-width: 80px;
}

.resource-buttons input[type="radio"]:checked + label {
  background: var(--primary-gold);
  color: var(--text-dark);
  border-color: var(--primary-gold-dark);
}

.help-text {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: 0.8rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

.map-suggestions {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-sm);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: var(--shadow-md);
}

.map-suggestions.active {
  display: block;
}

.map-suggestion-item {
  padding: var(--spacing-sm);
  cursor: pointer;
  border-bottom: 1px solid var(--border-secondary);
  transition: background 0.2s ease;
}

.map-suggestion-item:last-child {
  border-bottom: none;
}

.map-suggestion-item:hover {
  background: var(--bg-hover);
}

/* ==========================================================================
   RESPONSIVE DESIGN
   ========================================================================== */

@media (max-width: 600px) {
  .modal-content {
    padding: var(--spacing-md);
    margin: var(--spacing-sm);
  }
  
  .form-section {
    padding: var(--spacing-sm);
  }
  
  .resource-buttons {
    flex-direction: column;
  }
  
  .resource-buttons label {
    min-width: auto;
  }
}

/* ==========================================================================
   GENTLE MESSAGES - For when there's no data yet
   ========================================================================== */

.gentle-message {
  text-align: center;
  padding: var(--spacing-xl);
  background: rgba(255, 255, 255, 0.02);
  border: 1px dashed rgba(201, 170, 113, 0.3);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
}

.gentle-message .message-icon {
  font-size: 2.5rem;
  margin-bottom: var(--spacing-md);
  opacity: 0.8;
}

.gentle-message h4 {
  color: var(--primary-gold);
  font-family: 'Cinzel', serif;
  font-size: 1.3rem;
  margin-bottom: var(--spacing-sm);
  font-weight: 600;
}

.gentle-message p {
  margin-bottom: var(--spacing-sm);
  line-height: 1.5;
  color: var(--text-secondary);
}

.gentle-message small {
  color: var(--text-muted);
  font-style: italic;
}

.gentle-message-cell {
  padding: var(--spacing-lg);
  text-align: center;
}

/* Stats messages */
.stats-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-sm);
  border: 1px dashed rgba(201, 170, 113, 0.2);
  margin-bottom: var(--spacing-sm);
}

.stats-message:last-child {
  margin-bottom: 0;
}

.stats-message .stats-icon {
  font-size: 1.5rem;
  opacity: 0.8;
}

.stats-message .stats-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9rem;
}

.stats-message .stats-subtitle {
  color: var(--text-muted);
  font-size: 0.8rem;
  font-style: italic;
}

@media (max-width: 768px) {
  .gentle-message {
    padding: var(--spacing-lg);
  }
  
  .gentle-message .message-icon {
    font-size: 2rem;
  }
  
  .gentle-message h4 {
    font-size: 1.1rem;
  }
}

/* Table cell specific styling for gentle messages */
.leaderboard-table .gentle-message-cell {
  padding: var(--spacing-lg);
  text-align: center;
}

/* Stats list specific styling - higher specificity to override existing styles */
.stats-list .stats-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(255, 255, 255, 0.02);
  border-radius: var(--radius-sm);
  border: 1px dashed rgba(201, 170, 113, 0.2);
  margin-bottom: var(--spacing-sm);
}

.stats-list .stats-message:last-child {
  margin-bottom: 0;
}

.stats-list .stats-message .stats-icon {
  font-size: 1.5rem;
  opacity: 0.8;
}

.stats-list .stats-message .stats-title {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.9rem;
}

.stats-list .stats-message .stats-subtitle {
  color: var(--text-muted);
  font-size: 0.8rem;
  font-style: italic;
}

/* ==========================================================================
   PLAYER STATS MODAL
   ========================================================================== */

#player-stats-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 10000;
  display: none;
  align-items: center;
  justify-content: center;
}

#player-stats-modal.show {
  display: flex;
}

#player-stats-modal .modal-content {
  background: var(--bg-primary);
  border: 2px solid var(--primary-gold);
  border-radius: var(--radius-lg);
  max-width: 600px;
  width: 90%;
  max-height: 85vh;
  min-height: 400px;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  z-index: 10001;
  transition: max-height 0.3s ease;
}

/* When modal has expanded content, allow it to grow larger */
#player-stats-modal .modal-content.has-expanded-content {
  max-height: 95vh !important;
  width: 95% !important;
  max-width: 900px !important;
  transition: all 0.3s ease;
}

/* Make expanded content more prominent */
#player-stats-modal .match-details-expanded {
  background: rgba(0, 0, 0, 0.4) !important;
  border: 1px solid rgba(212, 175, 55, 0.2) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

/* Responsive modal expansion */
@media (min-width: 1200px) {
  #player-stats-modal .modal-content.has-expanded-content {
    max-width: 1000px !important;
  }
}

@media (min-width: 1400px) {
  #player-stats-modal .modal-content.has-expanded-content {
    max-width: 1100px !important;
  }
}

/* Ensure matches container has proper spacing */
#player-stats-modal .matches-container {
  padding: 1rem;
  overflow: visible;
  max-height: 70vh;
  overflow-y: auto;
}

#player-stats-modal .matches-container::-webkit-scrollbar {
  width: 8px;
}

#player-stats-modal .matches-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

#player-stats-modal .matches-container::-webkit-scrollbar-thumb {
  background: rgba(212, 175, 55, 0.5);
  border-radius: 4px;
}

#player-stats-modal .matches-container::-webkit-scrollbar-thumb:hover {
  background: rgba(212, 175, 55, 0.7);
}

#player-stats-modal .modal-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-secondary);
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(201, 170, 113, 0.1);
  text-align: center;
}

#player-stats-modal .modal-header h2 {
  color: var(--primary-gold);
  font-family: 'Cinzel', serif;
  margin: 0;
  font-size: 1.5rem;
}

#player-stats-modal .close-modal {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(220, 38, 38, 0.15);
  border: 2px solid rgba(220, 38, 38, 0.4);
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #dc2626;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 1001;
  text-decoration: none;
  line-height: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

#player-stats-modal .close-modal:hover {
  background: rgba(220, 38, 38, 0.3);
  border-color: rgba(220, 38, 38, 0.7);
  color: #ffffff;
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.5);
}

#player-stats-modal .modal-body {
  padding: 0;
  overflow: visible;
  position: relative;
  z-index: 10002;
}

.player-stats-overview {
  color: var(--text-primary);
}

.player-stats-overview .player-header {
  margin-bottom: var(--spacing-lg);
}

.player-rank-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.player-rank-large {
  width: 80px;
  height: 80px;
  object-fit: contain;
}

.player-rank-info h3 {
  color: var(--primary-gold);
  font-family: 'Cinzel', serif;
  font-size: 1.8rem;
  margin: 0 0 var(--spacing-sm) 0;
}

.rank-name {
  color: var(--text-secondary);
  font-size: 1.1rem;
  margin: 0 0 var(--spacing-xs) 0;
}

.mmr {
  color: var(--primary-gold);
  font-weight: 600;
  font-size: 1.2rem;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background: rgba(255, 255, 255, 0.02);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  text-align: center;
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--primary-gold);
}

.stat-card h4 {
  color: var(--text-secondary);
  font-size: 0.9rem;
  margin: 0 0 var(--spacing-sm) 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: var(--text-primary);
}

.stat-value.wins {
  color: #4CAF50;
}

.stat-value.losses {
  color: #f44336;
}

.stat-value.excellent {
  color: #4CAF50;
}

.stat-value.good {
  color: #8BC34A;
}

.stat-value.average {
  color: #FF9800;
}

.stat-value.poor {
  color: #f44336;
}

.player-details {
  border-top: 1px solid var(--border-secondary);
  padding-top: var(--spacing-md);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item .label {
  color: var(--text-secondary);
  font-weight: 500;
}

.detail-item .value {
  color: var(--text-primary);
  font-weight: 600;
}

.detail-item .value.claimed {
  color: #4CAF50;
}

.detail-item .value.unclaimed {
  color: var(--text-secondary);
}

/* Mobile responsiveness */
@media (max-width: 600px) {
  #player-stats-modal .modal-content {
    width: 95%;
    margin: var(--spacing-md);
  }
  
  #player-stats-modal .close-modal {
    top: 10px;
    right: 15px;
    width: 35px;
    height: 35px;
    font-size: 16px;
  }
  
  .player-rank-info {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }
}

/* Game Tabs Styling */
.game-tabs-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.game-tabs {
  display: flex;
  background: rgba(30, 30, 30, 0.9);
  border-radius: 12px;
  padding: 8px;
  gap: 8px;
  border: 2px solid rgba(218, 165, 32, 0.3);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.game-tab {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: transparent;
  color: #ddd;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Cinzel', serif;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 180px;
}

.game-tab:hover {
  background: rgba(218, 165, 32, 0.1);
  color: #DAA520;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(218, 165, 32, 0.2);
}

.game-tab.active {
  background: linear-gradient(135deg, rgba(218, 165, 32, 0.2), rgba(184, 134, 11, 0.2));
  color: #DAA520;
  border: 1px solid rgba(218, 165, 32, 0.4);
  box-shadow: 0 4px 16px rgba(218, 165, 32, 0.3);
}

.game-tab-icon {
  font-size: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(218, 165, 32, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.game-tab.active .game-tab-icon {
  background: rgba(218, 165, 32, 0.2);
  color: #FFD700;
}

.game-tab-info {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.game-tab-title {
  font-size: 16px;
  font-weight: 600;
  line-height: 1.2;
}

.game-tab-subtitle {
  font-size: 12px;
  opacity: 0.8;
  font-weight: 400;
  color: #bbb;
}

.game-tab.active .game-tab-subtitle {
  color: #FFD700;
  opacity: 0.9;
}

/* War-specific color themes */
.game-tab[data-game-type="war1"]:hover,
.game-tab[data-game-type="war1"].active {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.2), rgba(160, 82, 45, 0.2));
  color: #CD853F;
  border-color: rgba(139, 69, 19, 0.4);
  box-shadow: 0 4px 16px rgba(139, 69, 19, 0.3);
}

.game-tab[data-game-type="war3"]:hover,
.game-tab[data-game-type="war3"].active {
  background: linear-gradient(135deg, rgba(128, 0, 128, 0.2), rgba(75, 0, 130, 0.2));
  color: #9370DB;
  border-color: rgba(128, 0, 128, 0.4);
  box-shadow: 0 4px 16px rgba(128, 0, 128, 0.3);
}

/* Map Search Styling */
.map-search {
  margin: 0 10px;
}

.map-search .search-input-group {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(30, 30, 30, 0.8);
  border: 2px solid rgba(218, 165, 32, 0.3);
  border-radius: 8px;
  padding: 4px;
  transition: all 0.3s ease;
}

.map-search .search-input-group:focus-within {
  border-color: rgba(218, 165, 32, 0.6);
  box-shadow: 0 0 20px rgba(218, 165, 32, 0.2);
}

#map-search {
  background: transparent;
  border: none;
  color: #fff;
  padding: 12px 16px;
  font-size: 14px;
  min-width: 200px;
  font-family: 'Cinzel', serif;
}

#map-search::placeholder {
  color: #999;
  font-style: italic;
}

#map-search:focus {
  outline: none;
}

#map-search-btn {
  background: rgba(218, 165, 32, 0.2);
  color: #DAA520;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
  font-family: 'Cinzel', serif;
  font-weight: 500;
}

#map-search-btn:hover {
  background: rgba(218, 165, 32, 0.3);
  transform: translateY(-1px);
}

#clear-map-search-btn {
  background: rgba(220, 20, 60, 0.2);
  color: #DC143C;
  border: none;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  display: none;
  transition: all 0.3s ease;
}

#clear-map-search-btn:hover {
  background: rgba(220, 20, 60, 0.3);
  transform: translateY(-1px);
}

/* Enhanced controls section layout - CENTERED HORIZONTAL LAYOUT FOR ALL GAME TYPES */
.controls-section {
  background: var(--bg-secondary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-primary);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
}

/* Ensure all controls sections use consistent display style */
#wc1-controls,
#wc2-wc3-controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
}

#wc1-controls[style*="display: none"],
#wc2-wc3-controls[style*="display: none"] {
  display: none;
}

/* Remove vertical margins for horizontal layout */
.controls-section .player-search,
.controls-section .map-search,
.controls-section .match-type-filter {
  margin-bottom: 0;
  flex: 0 0 auto;
}

/* Ensure search sections take appropriate space while maintaining center alignment */
.controls-section .player-search {
  flex: 0 1 280px;
  min-width: 250px;
  max-width: 350px;
}

.controls-section .map-search {
  flex: 0 1 250px;
  min-width: 200px;
  max-width: 300px;
}

.controls-section .match-type-filter {
  flex: 0 0 auto;
}

/* Ensure identical styling for all search inputs */
#player-search,
#wc1-player-search,
#map-search {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-size: 1rem;
  padding: var(--spacing-md);
  padding-right: 70px; /* Make room for the label */
  outline: none;
  font-family: 'Cinzel', serif;
}

/* Search labels positioned inside the input on the right */
.search-label {
  position: absolute;
  right: 80px; /* Position to the left of the search button */
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
  pointer-events: none;
  background: var(--bg-tertiary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
  font-family: 'Cinzel', serif;
}

#player-search::placeholder,
#wc1-player-search::placeholder,
#map-search::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Ensure identical styling for all search buttons */
#search-btn,
#wc1-search-btn,
#map-search-btn {
  background: linear-gradient(135deg, var(--primary-gold) 0%, #b8860b 100%);
  color: var(--bg-primary);
  border: none;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  white-space: nowrap;
  flex-shrink: 0;
}

/* All clear buttons now inherit from .btn-clear above */

/* Responsive adjustments for centered horizontal layout */
@media (max-width: 1200px) {
  .controls-section {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
  }
  
  .controls-section .player-search,
  .controls-section .map-search {
    flex: 0 1 auto;
    min-width: auto;
    max-width: 100%;
    width: 100%;
  }
  
  .controls-section .match-type-filter {
    justify-content: center;
    width: 100%;
  }
  
  .filter-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .controls-section {
    padding: var(--spacing-md);
    text-align: center;
  }
  
  .search-input-group {
    max-width: 100%;
  }
  
  .filter-buttons {
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }
}

/* vs AI button styling */
.filter-btn[data-match-type="vsai"] {
  background: rgba(34, 139, 34, 0.2);
  color: #32CD32;
  border-color: rgba(34, 139, 34, 0.4);
}

.filter-btn[data-match-type="vsai"]:hover {
  background: rgba(34, 139, 34, 0.3);
  box-shadow: 0 4px 16px rgba(34, 139, 34, 0.2);
}

.filter-btn[data-match-type="vsai"].active {
  background: rgba(34, 139, 34, 0.4);
  box-shadow: 0 4px 16px rgba(34, 139, 34, 0.3);
}

/* WC1 Specific Controls - REMOVED */

/* WC1 Report Match Modal Styling */
#wc1-report-match-modal .race-buttons,
#wc1-report-match-modal .result-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

#wc1-report-match-modal .race-buttons label,
#wc1-report-match-modal .result-buttons label {
  flex: 1;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

#wc1-report-match-modal .race-buttons label:hover,
#wc1-report-match-modal .result-buttons label:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 193, 7, 0.6);
  transform: translateY(-2px);
}

#wc1-report-match-modal input[type="radio"]:checked + label {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.1));
  border-color: #ffc107;
  color: #ffc107;
}

#wc1-report-match-modal .result-win:hover,
#wc1-report-match-modal input[type="radio"]:checked + .result-win {
  background: linear-gradient(135deg, rgba(40, 167, 69, 0.3), rgba(40, 167, 69, 0.1));
  border-color: #28a745;
  color: #28a745;
}

#wc1-report-match-modal .result-loss:hover,
#wc1-report-match-modal input[type="radio"]:checked + .result-loss {
  background: linear-gradient(135deg, rgba(220, 53, 69, 0.3), rgba(220, 53, 69, 0.1));
  border-color: #dc3545;
  color: #dc3545;
}

#wc1-report-match-modal input[type="radio"] {
  display: none;
}

/* WC1 Match Type Buttons */
#wc1-report-match-modal .match-type-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

#wc1-report-match-modal .match-type-btn {
  flex: 1;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

#wc1-report-match-modal .match-type-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 193, 7, 0.6);
  transform: translateY(-2px);
}

#wc1-report-match-modal input[name="matchType"]:checked + .match-type-btn {
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.3), rgba(255, 193, 7, 0.1));
  border-color: #ffc107;
  color: #ffc107;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .game-tabs {
    flex-direction: column;
    width: 100%;
  }
  
  .game-tab {
    min-width: auto;
    width: 100%;
    justify-content: center;
  }
  
  .controls-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .player-search,
  .map-search {
    width: 100%;
    margin: 0;
  }
  
  .search-input-group {
    width: 100%;
  }
  
  #player-search,
  #map-search {
    min-width: auto;
    width: 100%;
  }

  #wc1-report-match-modal .race-buttons,
  #wc1-report-match-modal .result-buttons {
    flex-direction: column;
  }

  #wc1-report-match-modal .race-buttons label,
  #wc1-report-match-modal .result-buttons label {
    width: 100%;
  }
}

/* WC1 Empty State Styling */
.wc1-empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--wc1-text-color);
}

.wc1-empty-state i {
  font-size: 4rem;
  color: var(--wc1-accent-gold);
  margin-bottom: 1rem;
  display: block;
}

.wc1-empty-state h3 {
  color: var(--wc1-accent-gold);
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-weight: bold;
}

.wc1-empty-state p {
  margin-bottom: 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

.wc1-empty-state .btn {
  background: linear-gradient(135deg, var(--wc1-accent-gold), #c4941a);
  border: none;
  color: var(--wc1-bg-dark);
  font-weight: bold;
  padding: 12px 24px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.wc1-empty-state .btn:hover {
  background: linear-gradient(135deg, #c4941a, var(--wc1-accent-gold));
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.3);
}

/* vs AI Badge */
.vs-ai-badge {
  background: linear-gradient(135deg, #8B4513, #A0522D);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: bold;
  margin-left: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Empty Matches Styling */
.empty-matches {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-muted);
}

.empty-matches i {
  font-size: 3rem;
  color: var(--wc1-accent-gold);
  margin-bottom: 1rem;
  display: block;
}

.empty-matches p {
  font-size: 1.1rem;
  margin: 0;
}

/* AI Player Styling */
.match-player.ai {
  color: #8B4513;
  font-weight: bold;
  font-style: italic;
}

/* WC1 Win Rate Classes */
.win-rate.excellent {
  color: #2ecc71;
  font-weight: bold;
}

.win-rate.good {
  color: #27ae60;
  font-weight: bold;
}

.win-rate.average {
  color: #f39c12;
  font-weight: bold;
}

.win-rate.below-average {
  color: #e74c3c;
  font-weight: bold;
}

/* Responsive Adjustments for WC1 */
@media (max-width: 768px) {
  .wc1-empty-state {
    padding: 2rem 1rem;
  }
  
  .wc1-empty-state i {
    font-size: 3rem;
  }
  
  .wc1-empty-state h3 {
    font-size: 1.3rem;
  }
  
  .vs-ai-badge {
    font-size: 0.7rem;
    padding: 1px 6px;
  }
}

@keyframes wc1-pulse {
  0%, 100% {
    box-shadow: 0 4px 12px rgba(218, 165, 32, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(218, 165, 32, 0.5);
  }
}

/* WC1 Modal Specific Styling - High Priority */
#wc1-report-match-modal {
  display: none; /* Hidden by default */
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 10000; /* Very high z-index to ensure it's on top */
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(5px);
}

#wc1-report-match-modal.show {
  display: flex; /* Show when has 'show' class */
}

#wc1-report-match-modal .modal-content {
  background: linear-gradient(135deg, var(--wc1-bg-dark), #2a1810);
  border: 2px solid var(--wc1-accent-gold);
  border-radius: 12px;
  padding: 2rem;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
  position: relative;
  z-index: 10001;
}

/* Error handling styles for when API calls fail */
.error-message {
  background: rgba(220, 20, 60, 0.1);
  border: 2px solid rgba(220, 20, 60, 0.3);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  color: #ff6b6b;
}

.error-content i {
  font-size: 2rem;
  color: #ff6b6b;
}

.retry-btn {
  background: rgba(220, 20, 60, 0.8);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: rgba(220, 20, 60, 1);
  transform: translateY(-1px);
}

.no-results {
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  color: #ccc;
  font-style: italic;
} 

/* Enhanced player info styling */
.player-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.player-name {
  cursor: pointer;
  color: #D4AF37;
  font-weight: 600;
  transition: color 0.2s ease;
}

.player-name:hover {
  color: #FFD700;
  text-decoration: underline;
} 

/* Responsive rank badge styling */
@media (max-width: 768px) {
  .leaderboard-table .rank-badge {
    width: 32px;
    height: 32px;
  }
  
  .leaderboard-table .rank-badge img {
    width: 24px;
    height: 24px;
  }
  
  .leaderboard-table .rank-info {
    gap: 8px;
  }
  
  .leaderboard-table .rank-number {
    font-size: 1rem;
    min-width: 25px;
  }
}

@media (max-width: 480px) {
  .leaderboard-table .rank-badge {
    width: 28px;
    height: 28px;
  }
  
  .leaderboard-table .rank-badge img {
    width: 20px;
    height: 20px;
  }
  
  .leaderboard-table .rank-info {
    gap: 6px;
  }
  
  .leaderboard-table .rank-number {
    font-size: 0.9rem;
    min-width: 20px;
  }
}
